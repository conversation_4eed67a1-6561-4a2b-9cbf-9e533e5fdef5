<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Unified Integration
 * Single integration class handling API, Email, and Test functionality
 * 
 * @since 1.0.0
 */
class BSA_Gift_Card_Integration extends WC_Integration
{
    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * API handler
     */
    private $api_handler;

    /**
     * Logger instance
     */
    private $logger;

    /**
     * Get singleton instance
     */
    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->id = 'bsa_gift_card';
        $this->method_title = __('BSA Gift Card', 'bsa-gift-card');
        $this->method_description = __('Konfiguration der BSA-Geschenkkartenfunktionalität einschließlich API, E-Mail-Benachrichtigungen und Testtools.', 'bsa-gift-card');

        // Load settings first
        $this->init_form_fields();
        $this->init_settings();

        // Initialize handlers after settings are loaded
        add_action('init', [$this, 'init_handlers'], 20);

        // Migrate existing settings once
        $this->migrate_legacy_settings();

        // Hook into WooCommerce
        $this->init_hooks();
    }

    /**
     * Initialize handler classes
     */
    public function init_handlers()
    {
        // Initialize logger safely
        if (class_exists('BSA_Gift_Card_Logger')) {
            $this->logger = BSA_Gift_Card_Logger::get_instance();
        }

        // Initialize API handler safely
        if (class_exists('BSA_Gift_Card_API_Handler')) {
            $this->api_handler = new BSA_Gift_Card_API_Handler($this);
        }
    }

    /**
     * Initialize hooks
     */
    private function init_hooks()
    {
        add_action('woocommerce_update_options_integration_' . $this->id, [$this, 'process_admin_options']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);

        // AJAX handlers - with safety checks
        add_action('wp_ajax_bsa_test_api_connection', [$this, 'handle_api_test']);
        add_action('wp_ajax_bsa_send_test_email', [$this, 'handle_email_test']);
        add_action('wp_ajax_bsa_test_pdf_generation', [$this, 'handle_test_pdf']);
        add_action('wp_ajax_bsa_test_order_simulation', [$this, 'handle_test_order']);
    }

    /**
     * Initialize form fields
     */
    public function init_form_fields()
    {
        $this->form_fields = [
            // API Configuration Section
            'api_section_title' => [
                'title' => __('API Configuration', 'bsa-gift-card'),
                'type' => 'title',
                'description' => __('Configure connection to remote WooCommerce shop for coupon creation.', 'bsa-gift-card'),
            ],
            'api_enabled' => [
                'title' => __('Enable API', 'bsa-gift-card'),
                'type' => 'checkbox',
                'label' => __('Enable remote API coupon creation', 'bsa-gift-card'),
                'default' => 'no',
                'desc_tip' => __('When enabled, coupons will be created on the remote shop. When disabled, test mode is used.', 'bsa-gift-card'),
            ],
            'remote_url' => [
                'title' => __('Remote Shop URL', 'bsa-gift-card'),
                'type' => 'url',
                'description' => __('Full URL of the remote WooCommerce shop', 'bsa-gift-card'),
                'desc_tip' => true,
                'placeholder' => 'https://shop.bsa-akademie.de',
                'css' => 'min-width: 400px;',
            ],
            'consumer_key' => [
                'title' => __('Consumer Key', 'bsa-gift-card'),
                'type' => 'text',
                'description' => __('WooCommerce REST API Consumer Key', 'bsa-gift-card'),
                'desc_tip' => true,
                'css' => 'min-width: 400px;',
            ],
            'consumer_secret' => [
                'title' => __('Consumer Secret', 'bsa-gift-card'),
                'type' => 'password',
                'description' => __('WooCommerce REST API Consumer Secret', 'bsa-gift-card'),
                'desc_tip' => true,
                'css' => 'min-width: 400px;',
            ],

            // Email Notifications Section
            'email_section_title' => [
                'title' => __('Email Notifications', 'bsa-gift-card'),
                'type' => 'title',
                'description' => __('Configure email notifications for gift card processing.', 'bsa-gift-card'),
            ],
            'enable_notifications' => [
                'title' => __('Enable Email Notifications', 'bsa-gift-card'),
                'type' => 'checkbox',
                'label' => __('Send notifications for gift card events', 'bsa-gift-card'),
                'default' => 'yes',
            ],
            'notification_email' => [
                'title' => __('Notification Email', 'bsa-gift-card'),
                'type' => 'email',
                'description' => __('Email address for notifications', 'bsa-gift-card'),
                'desc_tip' => true,
                'default' => get_option('admin_email'),
                'css' => 'min-width: 300px;',
            ],

            // Development & Testing Section  
            'dev_section_title' => [
                'title' => __('Development & Testing', 'bsa-gift-card'),
                'type' => 'title',
                'description' => __('Tools for testing and debugging gift card functionality.', 'bsa-gift-card'),
            ],
            'enable_logging' => [
                'title' => __('Enable Detailed Logging', 'bsa-gift-card'),
                'type' => 'checkbox',
                'label' => __('Log all API requests and responses', 'bsa-gift-card'),
                'default' => 'yes',
            ],
        ];
    }

    /**
     * Process admin options
     */
    public function process_admin_options()
    {
        $result = parent::process_admin_options();

        // Sync to all systems
        $this->sync_settings_to_all_systems();

        return $result;
    }

    /**
     * Admin options - ENHANCED TEST ORDER SYSTEM
     */
    public function admin_options()
    {
?>
        <h2><?php echo esc_html($this->get_method_title()); ?></h2>
        <p><?php echo wp_kses_post($this->get_method_description()); ?></p>

        <table class="form-table">
            <?php $this->generate_settings_html(); ?>
        </table>

        <!-- Enhanced Test Tools Section -->
        <div style="background: #f9f9f9; padding: 20px; margin: 20px 0;">
            <h3><?php _e('Test Tools', 'bsa-gift-card'); ?></h3>

            <!-- API Connection Test -->
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('API Connection Test', 'bsa-gift-card'); ?></h4>
                <button type="button" id="test-api-connection" class="button button-primary">
                    <?php _e('Test API Connection', 'bsa-gift-card'); ?>
                </button>
                <div id="api-test-result" style="margin-top: 10px;"></div>
            </div>

            <!-- Enhanced Test Order Creation -->
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('Create Test Gift Card Order', 'bsa-gift-card'); ?></h4>
                <p style="color: #666; font-style: italic;"><?php _e('Create a new test order with custom gift card parameters and process it through the complete workflow.', 'bsa-gift-card'); ?></p>

                <div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                    <strong><?php _e('Test Order Details:', 'bsa-gift-card'); ?></strong>
                    <ul style="margin: 10px 0 0 20px; font-size: 13px; color: #666;">
                        <li><?php _e('✓ Neue Bestellung wird erstellt (nicht bearbeitet)', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ Automatische Gutschein-Erstellung über API', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ PDF-Generierung (falls Digital)', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ E-Mail-Benachrichtigung', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ Bestellung kann nach dem Test gelöscht werden', 'bsa-gift-card'); ?></li>
                    </ul>
                </div>

                <button type="button" id="create-test-order" class="button button-secondary" style="margin-right: 10px;">
                    <?php _e('Test-Bestellung erstellen & verarbeiten', 'bsa-gift-card'); ?>
                </button>

                <div id="test-order-results" style="margin-top: 15px;"></div>
            </div>

            <!-- Other Test Tools -->
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('Individual Component Tests', 'bsa-gift-card'); ?></h4>

                <button type="button" id="send-test-email" class="button" style="margin-right: 10px;">
                    <?php _e('Send Test Email', 'bsa-gift-card'); ?>
                </button>

                <button type="button" id="test-pdf-generation" class="button" style="margin-right: 10px;">
                    <?php _e('Test PDF Generation', 'bsa-gift-card'); ?>
                </button>

                <div id="component-test-results" style="margin-top: 10px;"></div>
            </div>

            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('Logs & Debug', 'bsa-gift-card'); ?></h4>
                <a href="<?php echo admin_url('admin.php?page=wc-status&tab=logs'); ?>" class="button" target="_blank">
                    <?php _e('Open WooCommerce Logs', 'bsa-gift-card'); ?>
                </a>
            </div>
        </div>

        <?php $this->add_enhanced_inline_admin_script(); ?>
    <?php
    }


    /**
     * Enhanced admin JavaScript with test order functionality
     */
    private function add_enhanced_inline_admin_script()
    {
        $nonce = wp_create_nonce('bsa_admin_nonce');
    ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                const BSATestAdmin = {
                    init: function() {
                        this.bindEvents();
                        this.handleFormatChange();
                    },

                    bindEvents: function() {
                        // Existing functionality
                        $('#test-api-connection').on('click', this.testAPIConnection);
                        $('#send-test-email').on('click', this.sendTestEmail);
                        $('#test-pdf-generation').on('click', this.testPDFGeneration);

                        // Enhanced test order functionality
                        $('#create-test-order').on('click', this.createTestOrder);
                        $('#test_format').on('change', this.handleFormatChange);
                    },

                    testAPIConnection: function(e) {
                        e.preventDefault();
                        const $button = $(this);
                        const $result = $('#api-test-result');

                        $button.prop('disabled', true).text('Testing...');
                        $result.html('<div style="color: blue;">Testing connection...</div>');

                        $.post(ajaxurl, {
                            action: 'bsa_test_api_connection',
                            nonce: '<?php echo $nonce; ?>',
                            remote_url: $('#woocommerce_bsa_gift_card_remote_url').val(),
                            consumer_key: $('#woocommerce_bsa_gift_card_consumer_key').val(),
                            consumer_secret: $('#woocommerce_bsa_gift_card_consumer_secret').val()
                        }, function(response) {
                            if (response.success) {
                                $result.html('<div style="color: green;">✓ ' + response.data.message + '</div>');
                            } else {
                                console.log('API not working');
                                this.sendTestEmail;
                                $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                                // TODO: email with test failed info
                            }
                            $button.prop('disabled', false).text('Test API Connection');
                        });
                    },

                    sendTestEmail: function(e) {
                        e.preventDefault();
                        const $button = $(this);
                        const $result = $('#component-test-results');

                        $button.prop('disabled', true).text('Sending...');

                        $.post(ajaxurl, {
                            action: 'bsa_send_test_email',
                            nonce: '<?php echo $nonce; ?>'
                        }, function(response) {
                            if (response.success) {
                                $result.html('<div style="color: green;">✓ ' + response.data.message + '</div>');
                            } else {
                                $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                            }
                            $button.prop('disabled', false).text('Send Test Email');
                        });
                    },

                    testPDFGeneration: function(e) {
                        e.preventDefault();
                        const $button = $(this);
                        const $result = $('#component-test-results');

                        $button.prop('disabled', true).text('Testing...');

                        $.post(ajaxurl, {
                            action: 'bsa_test_pdf_generation',
                            nonce: '<?php echo $nonce; ?>'
                        }, function(response) {
                            const color = response.success ? 'green' : 'red';
                            const icon = response.success ? '✓' : '✗';
                            $result.html('<div style="color: ' + color + ';">' + icon + ' ' + response.data.message + '</div>');
                            $button.prop('disabled', false).text('Test PDF Generation');
                        });
                    },

                    createTestOrder: function(e) {
                        e.preventDefault();
                        const $button = $(this);

                        const randomAmounts = [25, 50, 75, 100, 150, 200];
                        const randomTemplates = ['Freude schenken', 'Geburtstag', 'Ostern', 'Weihnachten'];
                        const randomFormats = ['digital', 'print'];
                        const randomFromNames = ['Max Mustermann', 'Lisa Weber', 'Thomas Müller', 'Sarah Fischer', 'Michael Schmidt'];
                        const randomToNames = ['Anna Schmidt', 'Peter Wagner', 'Julia Becker', 'Stefan Meyer', 'Marina Hoffmann'];
                        const orderTime = new Date().toLocaleTimeString();

                        const $result = $('#test-order-results');

                        // Get form data
                        const testData = {
                            amount: randomAmounts[Math.floor(Math.random() * randomAmounts.length)],
                            format: randomFormats[Math.floor(Math.random() * randomFormats.length)],
                            template: randomTemplates[Math.floor(Math.random() * randomTemplates.length)],
                            from_name: randomFromNames[Math.floor(Math.random() * randomFormats.length)],
                            to_name: randomFromNames[Math.floor(Math.random() * randomFormats.length)],
                            to_email: '<EMAIL>',
                            message: '#TEST-Bestellung: ' + orderTime,
                        };


                        $button.prop('disabled', true).text('Creating test order...');
                        $result.html('<div style="color: blue;">🔄 Creating test order with gift card processing...</div>');

                        $.post(ajaxurl, {
                            action: 'bsa_test_order_simulation',
                            nonce: '<?php echo $nonce; ?>',
                            ...testData
                        }, function(response) {
                            if (response.success) {
                                BSATestAdmin.displayTestOrderResults(response.data, $result);
                            } else {
                                $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                            }
                            $button.prop('disabled', false).text('Test-Bestellung erstellen & verarbeiten');
                        });
                    },

                    displayTestOrderResults: function(data, $result) {
                        let html = '<div style="background: #d1eddd; border: 1px solid #46b450; padding: 15px; border-radius: 4px; margin: 10px 0;">';
                        html += '<h4 style="color: #46b450; margin: 0 0 10px 0;">✅ Test Order Successfully Created & Processed</h4>';

                        // Order info
                        html += '<p><strong>Order:</strong> #' + data.order_number + ' ';
                        if (data.order_url) {
                            html += '<a href="' + data.order_url + '" target="_blank" style="color: #0073aa;">[View Order]</a>';
                        }
                        html += '</p>';

                        // Test parameters
                        if (data.test_params) {
                            html += '<p><strong>Parameters:</strong> €' + data.test_params.amount + ', ' +
                                data.test_params.format + ', ' + data.test_params.template + '</p>';
                        }

                        // Processing results
                        if (data.processing_details && data.processing_details.items_processed) {
                            html += '<h5 style="margin: 15px 0 10px 0;">Processing Results:</h5>';
                            html += '<ul style="margin: 0; padding-left: 20px;">';

                            data.processing_details.items_processed.forEach(function(item) {
                                html += '<li style="margin: 5px 0;">';
                                html += '<strong>' + item.product_name + '</strong> (€' + item.amount + ')<br>';
                                html += '<span style="font-size: 12px; color: #666;">';
                                html += 'Coupon: ' + (item.coupon_created ? '✅ ' + item.coupon_code : '❌ Failed');
                                if (item.format === 'digital') {
                                    html += ', PDF: ' + (item.pdf_created ? '✅ ' + item.pdf_name : '❌ Not created');
                                } else {
                                    html += ', Print file: ✅ Created';
                                }
                                html += '</span></li>';
                            });
                            html += '</ul>';
                        }

                        // Processing time
                        if (data.processing_details && data.processing_details.processing_time_ms) {
                            html += '<p style="font-size: 12px; color: #666; margin-top: 10px;">';
                            html += '⏱️ Processing time: ' + data.processing_details.processing_time_ms + 'ms</p>';
                        }

                        html += '</div>';
                        $result.html(html);
                    },

                    handleFormatChange: function() {
                        const format = $('#test_format').val();
                        const $emailLabel = $('#test_email_label');
                        const $emailField = $('#test_to_email');

                        if (format === 'digital') {
                            $emailLabel.text('Empfänger E-Mail (required)').css('font-weight', 'bold');
                            $emailField.prop('required', true);
                        } else {
                            $emailLabel.text('Empfänger E-Mail (optional)').css('font-weight', 'normal');
                            $emailField.prop('required', false);
                        }
                    },

                };

                BSATestAdmin.init();
            });
        </script>
    <?php
    }



    /**
     * AJAX Handlers
     */
    public function handle_api_test()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        if ($this->api_handler) {
            $this->api_handler->handle_connection_test();
        } else {
            wp_send_json_error(['message' => 'API handler not available']);
        }
    }

    public function handle_email_test()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        $email = $this->get_option('notification_email', get_option('admin_email'));

        $sent = wp_mail(
            $email,
            'BSA Gift Card Test Email',
            'This is a test email from BSA Gift Card plugin. Email functionality is working!'
        );

        if ($sent) {
            wp_send_json_success(['message' => "Test email sent to {$email}"]);
        } else {
            wp_send_json_error(['message' => 'Failed to send email']);
        }
    }

    public function handle_test_pdf()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        if (!class_exists('BSA_Gift_Card_PDF_Generator')) {
            wp_send_json_error(['message' => 'PDF Generator class not found']);
            return;
        }

        try {
            $test_data = [
                'code' => 'TEST-' . strtoupper(substr(md5(uniqid()), 0, 8)),
                'price' => 75.00,
                'expire_date' => date('Y-m-d', strtotime('+3 years')),
                'to_name' => 'Test Recipient',
                'from_name' => 'Test Sender',
                'template' => 'Freude schenken',
                'message' => 'This is a test gift card.',
                'order_id' => 'TEST-ORDER'
            ];

            $pdf_generator = new BSA_Gift_Card_PDF_Generator();
            $result = $pdf_generator->generate_gift_card_pdf($test_data);

            if ($result) {
                wp_send_json_success(['message' => 'PDF generated successfully: ' . basename($result)]);
            } else {
                wp_send_json_error(['message' => 'PDF generation failed']);
            }
        } catch (Exception $e) {
            wp_send_json_error(['message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Enhanced test order handler - CREATES NEW ORDERS
     */
    public function handle_test_order()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        try {
            // Get test parameters from AJAX request
            $test_params = $this->get_test_order_parameters();

            // Validate parameters
            if (!$this->validate_test_parameters($test_params)) {
                wp_send_json_error(['message' => 'Invalid test parameters provided']);
            }

            // Create test order with gift card
            $test_order = $this->create_comprehensive_test_order($test_params);

            if (!$test_order) {
                wp_send_json_error(['message' => 'Failed to create test order']);
            }

            // Process the order through complete workflow
            $processing_result = $this->process_test_order_workflow($test_order->get_id());

            wp_send_json_success([
                'message' => 'Test order created and processed successfully!',
                'order_id' => $test_order->get_id(),
                'order_number' => $test_order->get_order_number(),
                'order_url' => $test_order->get_edit_order_url(),
                'processing_details' => $processing_result,
                'test_params' => $test_params
            ]);
        } catch (Exception $e) {
            wp_send_json_error(['message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Get test order parameters from AJAX request
     */
    private function get_test_order_parameters()
    {
        return [
            'amount' => floatval($_POST['amount'] ?? 75.00),
            'format' => sanitize_text_field($_POST['format'] ?? 'digital'),
            'template' => sanitize_text_field($_POST['template'] ?? 'Freude schenken'),
            'from_name' => sanitize_text_field($_POST['from_name'] ?? 'Max Mustermann'),
            'to_name' => sanitize_text_field($_POST['to_name'] ?? 'Anna Schmidt'),
            'to_email' => sanitize_email($_POST['to_email'] ?? '<EMAIL>'),
            'message' => sanitize_textarea_field($_POST['message'] ?? ''),
            // 'random_data' => filter_var($_POST['random_data'] ?? false, FILTER_VALIDATE_BOOLEAN)
        ];
    }

    /**
     * Validate test parameters
     */
    private function validate_test_parameters($params)
    {
        // Validate amount
        if ($params['amount'] < 10 || $params['amount'] > 1000) {
            return false;
        }

        // Validate format
        if (!in_array($params['format'], ['digital', 'print'])) {
            return false;
        }

        // Validate template
        $valid_templates = ['Freude schenken', 'Geburtstag', 'Ostern', 'Weihnachten'];
        if (!in_array($params['template'], $valid_templates)) {
            return false;
        }

        // Validate email for digital format
        if ($params['format'] === 'digital' && !is_email($params['to_email'])) {
            return false;
        }

        // Validate required names
        if (empty(trim($params['from_name'])) || empty(trim($params['to_name']))) {
            return false;
        }

        return true;
    }

    /**
     * Create comprehensive test order with all gift card data - FIXED VERSION
     */
    private function create_comprehensive_test_order($params)
    {

        $gift_card_products = get_posts([
            'post_type' => 'product',
            'tax_query' => [
                [
                    'taxonomy' => 'product_type',
                    'field' => 'slug',
                    'terms' => 'bsa-gift-card'
                ]
            ],
            'posts_per_page' => 1,
            'post_status' => 'publish'
        ]);

        if (empty($gift_card_products)) {
            // Try alternative methods to find or create BSA gift card product
            $gift_card_products = $this->find_or_suggest_gift_card_product();
        }

        if (empty($gift_card_products)) {
            throw new Exception('No BSA gift card products found. ');
        }

        $product_id = $gift_card_products[0]->ID;
        $product = wc_get_product($product_id);

        // Verify the product is actually the right type
        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            throw new Exception('Found product is not a BSA gift card type. Product Type: ' . ($product ? $product->get_type() : 'unknown'));
        }

        // Generate random data if requested TODO: Remove
        // if ($params['random_data']) {
        //     $params = $this->apply_random_test_data($params);
        // }

        // Create order
        $order = wc_create_order();
        if (!$order) {
            throw new Exception('Failed to create WooCommerce order');
        }

        // Set customer data (hardcoded for testing)
        $customer_data = $this->get_test_customer_data($params['format']);
        $this->apply_customer_data_to_order($order, $customer_data);

        // Add gift card item with test data
        $item = new WC_Order_Item_Product();
        $item->set_product($product);
        $item->set_quantity(1);
        $item->set_total($params['amount']);

        // Add ALL gift card meta data
        $gift_card_meta = [
            'gift_card_price' => $params['amount'],
            'gift_card_from_name' => $params['from_name'],
            'gift_card_to_name' => $params['to_name'],
            'gift_card_template' => $params['template'],
            'gift_card_format' => $params['format'],
            'gift_card_message' => $params['message']
        ];

        // Add format-specific fields
        if ($params['format'] === 'digital') {
            $gift_card_meta['gift_card_to_email'] = $params['to_email'];
        }

        foreach ($gift_card_meta as $key => $value) {
            $item->add_meta_data($key, $value);
        }

        $order->add_item($item);

        // Set order totals and status
        $order->set_total($params['amount']);
        $order->set_status('completed'); // Set to completed to trigger processing

        // Add order note
        $order->add_order_note(sprintf(
            'TEST ORDER: BSA Gift Card test order (Format: %s, Template: %s, Amount: €%.2f) - Can be deleted after testing',
            $params['format'],
            $params['template'],
            $params['amount']
        ));

        $order->save();

        return $order;
    }

    /**
     * Find or suggest gift card product creation
     */
    private function find_or_suggest_gift_card_product()
    {
        // Try to find any product that might be a gift card
        $all_products = get_posts([
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ]);

        $potential_gift_cards = [];
        foreach ($all_products as $product_post) {
            $product = wc_get_product($product_post->ID);
            if ($product && $product->get_type() === 'bsa-gift-card') {
                $potential_gift_cards[] = $product_post;
            }
        }

        if (!empty($potential_gift_cards)) {
            error_log('BSA Gift Card: Found ' . count($potential_gift_cards) . ' BSA gift card products via fallback method');
            return $potential_gift_cards;
        }

        // Log debugging information
        $this->log_product_type_debug();

        return [];
    }

    /**
     * Log debug information about products and types
     */
    private function log_product_type_debug()
    {
        // Get all products for debugging
        $all_products = get_posts([
            'post_type' => 'product',
            'posts_per_page' => 10,
            'post_status' => ['publish', 'draft']
        ]);

        $debug_info = [
            'total_products' => count($all_products),
            'product_details' => []
        ];

        foreach ($all_products as $product_post) {
            $product = wc_get_product($product_post->ID);
            if ($product) {
                $product_terms = get_the_terms($product_post->ID, 'product_type');
                $debug_info['product_details'][] = [
                    'id' => $product_post->ID,
                    'title' => $product_post->post_title,
                    'type' => $product->get_type(),
                    'status' => $product_post->post_status,
                    'taxonomy_terms' => $product_terms ? wp_list_pluck($product_terms, 'slug') : []
                ];
            }
        }

        error_log('BSA Gift Card Debug - Products found: ' . wp_json_encode($debug_info, JSON_PRETTY_PRINT));

        // Also check if the product type is properly registered
        $registered_types = wc_get_product_types();
        error_log('BSA Gift Card Debug - Registered product types: ' . wp_json_encode($registered_types, JSON_PRETTY_PRINT));
    }


    /**
     * Apply random test data to parameters
     */
    private function apply_random_test_data($params)
    {
        $random_names = [
            'from' => ['Max Mustermann', 'Lisa Weber', 'Thomas Müller', 'Sarah Fischer', 'Michael Schmidt'],
            'to' => ['Anna Schmidt', 'Peter Wagner', 'Julia Becker', 'Stefan Meyer', 'Marina Hoffmann']
        ];

        $random_amounts = [25, 50, 75, 100, 150, 200];
        $random_templates = ['Freude schenken', 'Geburtstag', 'Ostern', 'Weihnachten'];
        $random_formats = ['digital', 'print'];

        $messages = [
            'Herzlichen Glückwunsch zu Deinem Geburtstag!',
            'Viel Freude mit diesem kleinen Geschenk!',
            'Ich hoffe, Du findest etwas Schönes für Dich!',
            'Alles Gute und viel Erfolg weiterhin!',
            'Ein kleines Dankeschön für Deine Hilfe!'
        ];

        $params['amount'] = $random_amounts[array_rand($random_amounts)];
        $params['template'] = $random_templates[array_rand($random_templates)];
        $params['format'] = $random_formats[array_rand($random_formats)];
        $params['from_name'] = $random_names['from'][array_rand($random_names['from'])];
        $params['to_name'] = $random_names['to'][array_rand($random_names['to'])];
        $params['message'] = $messages[array_rand($messages)];

        // Generate random email
        $domains = ['example.com', 'test.de', 'musterfirma.de'];
        $email_prefix = strtolower(str_replace(' ', '.', $params['to_name']));
        $params['to_email'] = $email_prefix . '@' . $domains[array_rand($domains)];

        return $params;
    }

    /**
     * Get test customer data (hardcoded + some variety)
     */
    private function get_test_customer_data($format)
    {
        $customer_variations = [
            [
                'first_name' => 'Max',
                'last_name' => 'Mustermann',
                'email' => '<EMAIL>',
                'phone' => '+49 (0) 123 456789',
                'address_1' => 'Musterstraße 123',
                'city' => 'Musterstadt',
                'postcode' => '12345',
                'country' => 'DE'
            ],
            [
                'first_name' => 'Lisa',
                'last_name' => 'Weber',
                'email' => '<EMAIL>',
                'phone' => '+49 (0) 987 654321',
                'address_1' => 'Beispielweg 456',
                'city' => 'Beispielort',
                'postcode' => '54321',
                'country' => 'DE'
            ],
            [
                'first_name' => 'Thomas',
                'last_name' => 'Schmidt',
                'email' => '<EMAIL>',
                'phone' => '+49 (0) 555 123456',
                'address_1' => 'Teststraße 789',
                'city' => 'Testdorf',
                'postcode' => '67890',
                'country' => 'DE'
            ]
        ];

        // Use random customer data
        $customer = $customer_variations[array_rand($customer_variations)];

        // For print format, ensure proper shipping address
        if ($format === 'print') {
            // You can modify shipping address here if needed
            $customer['shipping_note'] = 'Geschenkgutschein - Versand an Empfänger';
        }

        return $customer;
    }

    /**
     * Apply customer data to order
     */
    private function apply_customer_data_to_order($order, $customer_data)
    {
        // Billing data
        $order->set_billing_first_name($customer_data['first_name']);
        $order->set_billing_last_name($customer_data['last_name']);
        $order->set_billing_email($customer_data['email']);
        $order->set_billing_phone($customer_data['phone']);
        $order->set_billing_address_1($customer_data['address_1']);
        $order->set_billing_city($customer_data['city']);
        $order->set_billing_postcode($customer_data['postcode']);
        $order->set_billing_country($customer_data['country']);

        // Shipping data (same as billing for test)
        $order->set_shipping_first_name($customer_data['first_name']);
        $order->set_shipping_last_name($customer_data['last_name']);
        $order->set_shipping_address_1($customer_data['address_1']);
        $order->set_shipping_city($customer_data['city']);
        $order->set_shipping_postcode($customer_data['postcode']);
        $order->set_shipping_country($customer_data['country']);

        $order->set_customer_id(0); // Guest customer for test
    }

    /**
     * Process test order through complete workflow
     */
    private function process_test_order_workflow($order_id)
    {
        // Initialize coupon generator
        if (!class_exists('BSA_Gift_Card_Coupon_Generator')) {
            throw new Exception('Coupon generator class not available');
        }

        $generator = new BSA_Gift_Card_Coupon_Generator();

        // Enable debug mode for this test
        add_filter('bsa_gift_card_debug_mode', '__return_true');

        // Process the order
        $processing_start = microtime(true);
        $result = $generator->generate_coupons_from_order($order_id);
        $processing_time = round((microtime(true) - $processing_start) * 1000);

        // Remove debug filter
        remove_filter('bsa_gift_card_debug_mode', '__return_true');

        // Get detailed results
        $detailed_results = $this->get_detailed_processing_results($order_id);
        $detailed_results['processing_time_ms'] = $processing_time;
        $detailed_results['workflow_success'] = $result;

        return $detailed_results;
    }

    /**
     * Get detailed processing results
     */
    private function get_detailed_processing_results($order_id)
    {
        $order = wc_get_order($order_id);
        $results = [
            'order_id' => $order_id,
            'order_status' => $order->get_status(),
            'processing_completed' => $order->get_meta('_bsa_gift_card_coupons_generated') === 'yes',
            'items_processed' => [],
            'coupons_created' => [],
            'pdfs_generated' => [],
            'files_created' => []
        ];

        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            if ($product && $product->get_type() === 'bsa-gift-card') {
                $coupon_code = wc_get_order_item_meta($item_id, '_gift_card_coupon_code', true);
                $pdf_path = wc_get_order_item_meta($item_id, '_gift_card_pdf_path', true);
                $remote_id = wc_get_order_item_meta($item_id, '_gift_card_remote_id', true);

                $item_result = [
                    'item_id' => $item_id,
                    'product_name' => $item->get_name(),
                    'amount' => wc_get_order_item_meta($item_id, 'gift_card_price', true),
                    'format' => wc_get_order_item_meta($item_id, 'gift_card_format', true),
                    'template' => wc_get_order_item_meta($item_id, 'gift_card_template', true),
                    'coupon_code' => $coupon_code,
                    'coupon_created' => !empty($coupon_code),
                    'remote_id' => $remote_id,
                    'pdf_path' => $pdf_path,
                    'pdf_created' => !empty($pdf_path) && file_exists($pdf_path)
                ];

                if (!empty($pdf_path) && file_exists($pdf_path)) {
                    $item_result['pdf_size'] = filesize($pdf_path);
                    $item_result['pdf_name'] = basename($pdf_path);
                    $results['files_created'][] = basename($pdf_path);
                }

                $results['items_processed'][] = $item_result;

                if (!empty($coupon_code)) {
                    $results['coupons_created'][] = $coupon_code;
                }
            }
        }

        return $results;
    }


    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook_suffix)
    {
        if (
            $hook_suffix !== 'woocommerce_page_wc-settings' ||
            !isset($_GET['section']) ||
            $_GET['section'] !== 'bsa_gift_card'
        ) {
            return;
        }

        wp_enqueue_script('jquery');
    }

    /**
     * Add inline admin JavaScript
     */
    private function add_inline_admin_script()
    {
        $nonce = wp_create_nonce('bsa_admin_nonce');
    ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                $('#test-api-connection').on('click', function() {
                    const $button = $(this);
                    const $result = $('#api-test-result');

                    $button.prop('disabled', true).text('Testing...');
                    $result.html('<div style="color: blue;">Testing connection...</div>');

                    $.post(ajaxurl, {
                        action: 'bsa_test_api_connection',
                        nonce: '<?php echo $nonce; ?>',
                        remote_url: $('#woocommerce_bsa_gift_card_remote_url').val(),
                        consumer_key: $('#woocommerce_bsa_gift_card_consumer_key').val(),
                        consumer_secret: $('#woocommerce_bsa_gift_card_consumer_secret').val()
                    }, function(response) {
                        if (response.success) {
                            $result.html('<div style="color: green;">✓ ' + response.data.message + '</div>');
                        } else {
                            $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                        }
                        $button.prop('disabled', false).text('Test API Connection');
                    });
                });

                $('#send-test-email').on('click', function() {
                    const $button = $(this);
                    const $result = $('#email-test-result');

                    $button.prop('disabled', true).text('Sending...');

                    $.post(ajaxurl, {
                        action: 'bsa_send_test_email',
                        nonce: '<?php echo $nonce; ?>'
                    }, function(response) {
                        if (response.success) {
                            $result.html('<div style="color: green;">✓ ' + response.data.message + '</div>');
                        } else {
                            $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                        }
                        $button.prop('disabled', false).text('Send Test Email');
                    });
                });

                $('#test-pdf-generation').on('click', function() {
                    const $button = $(this);

                    $button.prop('disabled', true).text('Testing...');
                    $('#test-pdf-results').html('<div style="color: blue;">Testing PDF generation...</div>');

                    $.post(ajaxurl, {
                        action: 'bsa_test_pdf_generation',
                        nonce: '<?php echo $nonce; ?>'
                    }, function(response) {
                        const color = response.success ? 'green' : 'red';
                        const icon = response.success ? '✓' : '✗';
                        $('#test-pdf-results').html('<div style="color: ' + color + ';">' + icon + ' ' + response.data.message + '</div>');
                        $button.prop('disabled', false).text('Test PDF Generation');
                    });
                });

                $('#test-order-processing').on('click', function() {
                    const orderId = $('#test-order-id').val();
                    const $button = $(this);

                    if (!orderId) {
                        $('#test-order-results').html('<div style="color: red;">Please enter an order ID</div>');
                        return;
                    }

                    $button.prop('disabled', true).text('Processing...');
                    $('#test-order-results').html('<div style="color: blue;">Testing order processing...</div>');

                    $.post(ajaxurl, {
                        action: 'bsa_test_order_simulation',
                        nonce: '<?php echo $nonce; ?>',
                        order_id: orderId
                    }, function(response) {
                        const color = response.success ? 'green' : 'red';
                        const icon = response.success ? '✓' : '✗';
                        $('#test-order-results').html('<div style="color: ' + color + ';">' + icon + ' ' + response.data.message + '</div>');
                        $button.prop('disabled', false).text('Test Order Processing');
                    });
                });
            });
        </script>
<?php
    }

    /**
     * Helper methods
     */
    private function sync_settings_to_all_systems()
    {
        $settings = [
            'enabled' => $this->get_option('api_enabled'),
            'remote_url' => $this->get_option('remote_url'),
            'consumer_key' => $this->get_option('consumer_key'),
            'consumer_secret' => $this->get_option('consumer_secret'),
            'enable_notifications' => $this->get_option('enable_notifications'),
            'notification_email' => $this->get_option('notification_email'),
            'enable_logging' => $this->get_option('enable_logging'),
        ];

        // Update unified options
        update_option('bsa_gift_card_settings', $settings);
        update_option('bsa_gift_card_api_settings', $settings);
        update_option('bsa_gift_card_email_settings', $settings);
    }

    private function migrate_legacy_settings()
    {
        static $migrated = false;
        if ($migrated) return;
        $migrated = true;

        $existing_settings = get_option($this->get_option_key(), []);
        if (!empty($existing_settings)) return;

        // Migrate from old separate options
        $legacy_api = get_option('bsa_gift_card_api_settings', []);
        $legacy_email = get_option('bsa_gift_card_email_settings', []);

        if (empty($legacy_api) && empty($legacy_email)) return;

        $merged = array_merge($legacy_api, $legacy_email);
        update_option($this->get_option_key(), $merged);

        if ($this->logger) {
            $this->logger->log_info('Settings migrated from legacy options', ['merged_count' => count($merged)]);
        }
    }

    /**
     * Get unified settings for external classes
     */
    public function get_unified_settings()
    {
        return [
            'api_enabled' => $this->get_option('api_enabled') === 'yes',
            'remote_url' => $this->get_option('remote_url'),
            'consumer_key' => $this->get_option('consumer_key'),
            'consumer_secret' => $this->get_option('consumer_secret'),
            'enable_notifications' => $this->get_option('enable_notifications') === 'yes',
            'notification_email' => $this->get_option('notification_email'),
            'enable_logging' => $this->get_option('enable_logging') === 'yes',
        ];
    }
}
