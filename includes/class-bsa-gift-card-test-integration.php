<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Test Integration - Simplified Version
 * Handles test tools and debugging functionality
 */
class BSA_Gift_Card_Test_Integration extends WC_Integration
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->id = 'bsa_gift_card_tests';
        $this->method_title = __('BSA Gift Card TESTS', 'bsa-gift-card');
        $this->method_description = __('Test tools and debugging functionality for BSA Gift Card system.', 'bsa-gift-card');

        // Load settings first
        $this->init_form_fields();
        $this->init_settings();

        // Hook into WooCommerce
        add_action('woocommerce_update_options_integration_' . $this->id, [$this, 'process_admin_options']);
    }

    /**
     * Initialize form fields (minimal for test tab)
     */
    public function init_form_fields()
    {
        $this->form_fields = [
            'test_info' => [
                'title' => __('Test Tools', 'bsa-gift-card'),
                'type' => 'title',
                'description' => __('This tab will contain test tools for the BSA Gift Card system. Currently under development.', 'bsa-gift-card'),
            ],
        ];
    }

    /**
     * Admin options - Test Tools Interface
     */
    public function admin_options()
    {
?>
        <h2><?php echo esc_html($this->method_title); ?></h2>
        <p><?php echo wp_kses_post($this->method_description); ?></p>

        <table class="form-table">
            <?php $this->generate_settings_html(); ?>
        </table>

        <!-- Simple Test Tools Placeholder -->
        <div style="background: #f9f9f9; padding: 20px; margin: 20px 0;">
            <h3><?php _e('Test Tools - Coming Soon', 'bsa-gift-card'); ?></h3>
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <p><?php _e('This tab will contain test tools for the BSA Gift Card system.', 'bsa-gift-card'); ?></p>
                <p><?php _e('For now, please use the test tools in the main "BSA Gift Card Settings" tab.', 'bsa-gift-card'); ?></p>
            </div>
        </div>
    <?php
    }
}
