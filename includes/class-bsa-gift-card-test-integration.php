<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Test Integration
 * Handles test tools and debugging functionality
 */
class BSA_Gift_Card_Test_Integration extends WC_Integration
{
    /**
     * Integration ID
     */
    public $id = 'bsa_gift_card_tests';

    /**
     * Logger instance
     */
    private $logger;

    /**
     * Main integration instance for accessing settings
     */
    private $main_integration;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->id = 'bsa_gift_card_tests';
        $this->method_title = __('BSA Gift Card TESTS', 'bsa-gift-card');
        $this->method_description = __('Test tools and debugging functionality for BSA Gift Card system.', 'bsa-gift-card');

        // Load settings first
        $this->init_form_fields();
        $this->init_settings();

        // Get main integration instance
        $this->main_integration = $this->get_main_integration();

        // Initialize handlers after settings are loaded
        add_action('init', [$this, 'init_handlers'], 20);

        // Hook into WooCommerce
        $this->init_hooks();
    }

    /**
     * Get main integration instance
     */
    private function get_main_integration()
    {
        $integrations = WC()->integrations->get_integrations();
        return isset($integrations['bsa_gift_card']) ? $integrations['bsa_gift_card'] : null;
    }

    /**
     * Initialize handler classes
     */
    public function init_handlers()
    {
        // Initialize logger safely
        if (class_exists('BSA_Gift_Card_Logger')) {
            $this->logger = BSA_Gift_Card_Logger::get_instance();
        }
    }

    /**
     * Initialize hooks
     */
    private function init_hooks()
    {
        add_action('woocommerce_update_options_integration_' . $this->id, [$this, 'process_admin_options']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);

        // AJAX handlers for test functionality
        add_action('wp_ajax_bsa_test_api_connection', [$this, 'handle_api_test']);
        add_action('wp_ajax_bsa_send_test_email', [$this, 'handle_email_test']);
        add_action('wp_ajax_bsa_test_pdf_generation', [$this, 'handle_test_pdf']);
        add_action('wp_ajax_bsa_test_order_simulation', [$this, 'handle_test_order']);
    }

    /**
     * Initialize form fields (minimal for test tab)
     */
    public function init_form_fields()
    {
        $this->form_fields = [
            'test_section_title' => [
                'title' => __('Test Configuration', 'bsa-gift-card'),
                'type' => 'title',
                'description' => __('Configure test parameters and debugging options.', 'bsa-gift-card'),
            ],
            'enable_test_mode' => [
                'title' => __('Enable Test Mode', 'bsa-gift-card'),
                'type' => 'checkbox',
                'label' => __('Enable comprehensive testing and debugging', 'bsa-gift-card'),
                'default' => 'yes',
                'desc_tip' => __('When enabled, detailed test results and debug information will be displayed.', 'bsa-gift-card'),
            ],
            'auto_delete_test_orders' => [
                'title' => __('Auto-delete Test Orders', 'bsa-gift-card'),
                'type' => 'checkbox',
                'label' => __('Automatically delete test orders after 24 hours', 'bsa-gift-card'),
                'default' => 'no',
                'desc_tip' => __('Test orders will be automatically cleaned up to prevent clutter.', 'bsa-gift-card'),
            ],
        ];
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook)
    {
        if (strpos($hook, 'wc-settings') !== false && isset($_GET['tab']) && $_GET['tab'] === 'integration') {
            wp_enqueue_script(
                'bsa-gift-card-test-admin',
                plugin_dir_url(dirname(__FILE__)) . 'assets/js/bsa-gift-card-admin-settings.js',
                ['jquery'],
                BSA_GIFT_CARD_VERSION,
                true
            );
        }
    }

    /**
     * Admin options - Test Tools Interface
     */
    public function admin_options()
    {
?>
        <h2><?php echo esc_html($this->get_method_title()); ?></h2>
        <p><?php echo wp_kses_post($this->get_method_description()); ?></p>

        <table class="form-table">
            <?php $this->generate_settings_html(); ?>
        </table>

        <!-- Test Tools Section -->
        <div style="background: #f9f9f9; padding: 20px; margin: 20px 0;">
            <h3><?php _e('Test Tools', 'bsa-gift-card'); ?></h3>

            <!-- API Connection Test -->
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('API Connection Test', 'bsa-gift-card'); ?></h4>
                <p style="color: #666; font-size: 13px;"><?php _e('Test the connection to the remote WooCommerce shop using current API settings.', 'bsa-gift-card'); ?></p>
                <button type="button" id="test-api-connection" class="button button-primary">
                    <?php _e('Test API Connection', 'bsa-gift-card'); ?>
                </button>
                <div id="api-test-result" style="margin-top: 10px;"></div>
            </div>

            <!-- Enhanced Test Order Creation -->
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('Create Test Gift Card Order', 'bsa-gift-card'); ?></h4>
                <p style="color: #666; font-style: italic;"><?php _e('Create a new test order with custom gift card parameters and process it through the complete workflow.', 'bsa-gift-card'); ?></p>

                <div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                    <strong><?php _e('Test Order Details:', 'bsa-gift-card'); ?></strong>
                    <ul style="margin: 10px 0 0 20px; font-size: 13px; color: #666;">
                        <li><?php _e('✓ Neue Bestellung wird erstellt (nicht bearbeitet)', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ Automatische Gutschein-Erstellung über API', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ PDF-Generierung (falls Digital)', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ E-Mail-Benachrichtigung', 'bsa-gift-card'); ?></li>
                        <li><?php _e('✓ Bestellung kann nach dem Test gelöscht werden', 'bsa-gift-card'); ?></li>
                    </ul>
                </div>

                <button type="button" id="create-test-order" class="button button-secondary" style="margin-right: 10px;">
                    <?php _e('Test-Bestellung erstellen & verarbeiten', 'bsa-gift-card'); ?>
                </button>

                <div id="test-order-results" style="margin-top: 15px;"></div>
            </div>

            <!-- Individual Component Tests -->
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('Individual Component Tests', 'bsa-gift-card'); ?></h4>

                <button type="button" id="send-test-email" class="button" style="margin-right: 10px;">
                    <?php _e('Send Test Email', 'bsa-gift-card'); ?>
                </button>

                <button type="button" id="test-pdf-generation" class="button" style="margin-right: 10px;">
                    <?php _e('Test PDF Generation', 'bsa-gift-card'); ?>
                </button>

                <div id="component-test-results" style="margin-top: 10px;"></div>
            </div>

            <!-- Logs & Debug -->
            <div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px;">
                <h4><?php _e('Logs & Debug', 'bsa-gift-card'); ?></h4>
                <a href="<?php echo admin_url('admin.php?page=wc-status&tab=logs'); ?>" class="button" target="_blank">
                    <?php _e('Open WooCommerce Logs', 'bsa-gift-card'); ?>
                </a>
            </div>
        </div>

        <?php $this->add_enhanced_inline_admin_script(); ?>
    <?php
    }

    /**
     * Enhanced admin JavaScript with test order functionality
     */
    private function add_enhanced_inline_admin_script()
    {
        $nonce = wp_create_nonce('bsa_admin_nonce');
    ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                const BSATestAdmin = {
                    init: function() {
                        this.bindEvents();
                        this.handleFormatChange();
                    },

                    bindEvents: function() {
                        // Test functionality
                        $('#test-api-connection').on('click', this.testAPIConnection);
                        $('#send-test-email').on('click', this.sendTestEmail);
                        $('#test-pdf-generation').on('click', this.testPDFGeneration);
                        $('#create-test-order').on('click', this.createTestOrder);
                        $('#test_format').on('change', this.handleFormatChange);
                    },

                    testAPIConnection: function(e) {
                        e.preventDefault();
                        const $button = $(this);
                        const $result = $('#api-test-result');

                        $button.prop('disabled', true).text('Testing...');
                        $result.html('<div style="color: blue;">Testing connection...</div>');

                        // Get settings from main integration tab
                        const remoteUrl = $('#woocommerce_bsa_gift_card_remote_url').val() || 
                                        $('input[name="woocommerce_bsa_gift_card_remote_url"]').val();
                        const consumerKey = $('#woocommerce_bsa_gift_card_consumer_key').val() || 
                                          $('input[name="woocommerce_bsa_gift_card_consumer_key"]').val();
                        const consumerSecret = $('#woocommerce_bsa_gift_card_consumer_secret').val() || 
                                             $('input[name="woocommerce_bsa_gift_card_consumer_secret"]').val();

                        $.post(ajaxurl, {
                            action: 'bsa_test_api_connection',
                            nonce: '<?php echo $nonce; ?>',
                            remote_url: remoteUrl,
                            consumer_key: consumerKey,
                            consumer_secret: consumerSecret
                        }, function(response) {
                            if (response.success) {
                                $result.html('<div style="color: green;">✓ ' + response.data.message + '</div>');
                            } else {
                                $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                            }
                            $button.prop('disabled', false).text('Test API Connection');
                        });
                    },

                    sendTestEmail: function(e) {
                        e.preventDefault();
                        const $button = $(this);
                        const $result = $('#component-test-results');

                        $button.prop('disabled', true).text('Sending...');

                        $.post(ajaxurl, {
                            action: 'bsa_send_test_email',
                            nonce: '<?php echo $nonce; ?>'
                        }, function(response) {
                            if (response.success) {
                                $result.html('<div style="color: green;">✓ ' + response.data.message + '</div>');
                            } else {
                                $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                            }
                            $button.prop('disabled', false).text('Send Test Email');
                        });
                    },

                    testPDFGeneration: function(e) {
                        e.preventDefault();
                        const $button = $(this);
                        const $result = $('#component-test-results');

                        $button.prop('disabled', true).text('Testing...');

                        $.post(ajaxurl, {
                            action: 'bsa_test_pdf_generation',
                            nonce: '<?php echo $nonce; ?>'
                        }, function(response) {
                            const color = response.success ? 'green' : 'red';
                            const icon = response.success ? '✓' : '✗';
                            $result.html('<div style="color: ' + color + ';">' + icon + ' ' + response.data.message + '</div>');
                            $button.prop('disabled', false).text('Test PDF Generation');
                        });
                    },

                    createTestOrder: function(e) {
                        e.preventDefault();
                        const $button = $(this);

                        const randomAmounts = [25, 50, 75, 100, 150, 200];
                        const randomTemplates = ['Freude schenken', 'Geburtstag', 'Ostern', 'Weihnachten'];
                        const randomFormats = ['digital', 'print'];
                        const randomFromNames = ['Max Mustermann', 'Lisa Weber', 'Thomas Müller', 'Sarah Fischer', 'Michael Schmidt'];
                        const randomToNames = ['Anna Schmidt', 'Peter Wagner', 'Julia Becker', 'Stefan Meyer', 'Marina Hoffmann'];
                        const orderTime = new Date().toLocaleTimeString();

                        const $result = $('#test-order-results');

                        // Get form data
                        const testData = {
                            amount: randomAmounts[Math.floor(Math.random() * randomAmounts.length)],
                            format: randomFormats[Math.floor(Math.random() * randomFormats.length)],
                            template: randomTemplates[Math.floor(Math.random() * randomTemplates.length)],
                            from_name: randomFromNames[Math.floor(Math.random() * randomFromNames.length)],
                            to_name: randomToNames[Math.floor(Math.random() * randomToNames.length)],
                            to_email: '<EMAIL>',
                            message: '#TEST-Bestellung: ' + orderTime,
                        };

                        $button.prop('disabled', true).text('Creating test order...');
                        $result.html('<div style="color: blue;">🔄 Creating test order with gift card processing...</div>');

                        $.post(ajaxurl, {
                            action: 'bsa_test_order_simulation',
                            nonce: '<?php echo $nonce; ?>',
                            ...testData
                        }, function(response) {
                            if (response.success) {
                                BSATestAdmin.displayTestOrderResults(response.data, $result);
                            } else {
                                $result.html('<div style="color: red;">✗ ' + response.data.message + '</div>');
                            }
                            $button.prop('disabled', false).text('Test-Bestellung erstellen & verarbeiten');
                        });
                    },

                    displayTestOrderResults: function(data, $result) {
                        let html = '<div style="background: #d1eddd; border: 1px solid #46b450; padding: 15px; border-radius: 4px; margin: 10px 0;">';
                        html += '<h4 style="color: #46b450; margin: 0 0 10px 0;">✅ Test Order Successfully Created & Processed</h4>';

                        // Order info
                        html += '<p><strong>Order:</strong> #' + data.order_number + ' ';
                        if (data.order_url) {
                            html += '<a href="' + data.order_url + '" target="_blank" style="color: #0073aa;">[View Order]</a>';
                        }
                        html += '</p>';

                        // Processing results
                        if (data.processing_time_ms) {
                            html += '<p><strong>Processing Time:</strong> ' + data.processing_time_ms + 'ms</p>';
                        }

                        if (data.coupon_code) {
                            html += '<p><strong>Generated Coupon:</strong> <code>' + data.coupon_code + '</code></p>';
                        }

                        if (data.pdf_generated) {
                            html += '<p><strong>PDF:</strong> ✅ Generated successfully</p>';
                        }

                        if (data.email_sent) {
                            html += '<p><strong>Email:</strong> ✅ Sent successfully</p>';
                        }

                        html += '</div>';
                        $result.html(html);
                    },

                    handleFormatChange: function() {
                        // Handle format-specific field visibility if needed
                    }
                };

                BSATestAdmin.init();
            });
        </script>
    <?php
    }

    /**
     * Handle API connection test
     */
    public function handle_api_test()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        // Delegate to main integration if available
        if ($this->main_integration && method_exists($this->main_integration, 'handle_api_test')) {
            $this->main_integration->handle_api_test();
        } else {
            wp_send_json_error(['message' => 'Main integration not available']);
        }
    }

    /**
     * Handle email test
     */
    public function handle_email_test()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        // Delegate to main integration if available
        if ($this->main_integration && method_exists($this->main_integration, 'handle_email_test')) {
            $this->main_integration->handle_email_test();
        } else {
            wp_send_json_error(['message' => 'Main integration not available']);
        }
    }

    /**
     * Handle PDF generation test
     */
    public function handle_test_pdf()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        // Delegate to main integration if available
        if ($this->main_integration && method_exists($this->main_integration, 'handle_test_pdf')) {
            $this->main_integration->handle_test_pdf();
        } else {
            wp_send_json_error(['message' => 'Main integration not available']);
        }
    }

    /**
     * Handle test order creation
     */
    public function handle_test_order()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        // Delegate to main integration if available
        if ($this->main_integration && method_exists($this->main_integration, 'handle_test_order')) {
            $this->main_integration->handle_test_order();
        } else {
            wp_send_json_error(['message' => 'Main integration not available']);
        }
    }
}
