<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card API Handler
 * 
 * Handles all API-related functionality in a clean, focused class
 * Works with the unified integration class
 * 
 * @since 1.0.0
 */
class BSA_Gift_Card_API_Handler
{
    /**
     * Integration instance
     */
    private $integration;

    /**
     * Logger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct(BSA_Gift_Card_Integration $integration)
    {
        $this->integration = $integration;
        $this->logger = BSA_Gift_Card_Logger::get_instance();
    }

    /**
     * Handle API connection test
     */
    public function handle_connection_test()
    {
        check_ajax_referer('bsa_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(['message' => 'Insufficient permissions']);
        }

        $remote_url = sanitize_url($_POST['remote_url'] ?? '');
        $consumer_key = sanitize_text_field($_POST['consumer_key'] ?? '');
        $consumer_secret = sanitize_text_field($_POST['consumer_secret'] ?? '');

        if (empty($remote_url) || empty($consumer_key) || empty($consumer_secret)) {
            wp_send_json_error(['message' => 'Please fill all API fields']);
        }

        $result = $this->test_api_connection($remote_url, $consumer_key, $consumer_secret);

        if ($result['success']) {
            // Update integration settings
            $this->integration->update_option('connection_status', 'connected');
            $this->integration->update_option('api_enabled', 'yes');
            
            wp_send_json_success(['message' => $result['message']]);
        } else {
            $this->integration->update_option('connection_status', 'failed');
            $this->integration->update_option('api_enabled', 'no');
            
            wp_send_json_error(['message' => $result['message']]);
        }
    }

    /**
     * Test API connection
     */
    private function test_api_connection($remote_url, $consumer_key, $consumer_secret)
    {
        $test_url = rtrim($remote_url, '/') . '/wp-json/wc/v3/system_status';
        
        $response = wp_remote_get($test_url, [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($consumer_key . ':' . $consumer_secret),
            ],
            'timeout' => 15,
            'sslverify' => $this->should_verify_ssl($remote_url),
        ]);

        if (is_wp_error($response)) {
            $this->logger->log_error('API connection test failed', [
                'url' => $remote_url,
                'error' => $response->get_error_message()
            ]);
            
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $response->get_error_message()
            ];
        }

        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);
            $wc_version = $body['environment']['version'] ?? 'unknown';
            
            $this->logger->log_info('API connection test successful', [
                'url' => $remote_url,
                'wc_version' => $wc_version
            ]);
            
            return [
                'success' => true,
                'message' => "Connection successful! Remote WooCommerce {$wc_version}"
            ];
        }

        $this->logger->log_error('API connection test failed', [
            'url' => $remote_url,
            'status_code' => $status_code
        ]);
        
        return [
            'success' => false,
            'message' => "Connection failed with HTTP {$status_code}"
        ];
    }

    /**
     * Create coupon via API
     */
    public function create_coupon($coupon_data)
    {
        $settings = $this->integration->get_unified_settings();
        
        if (!$settings['api_enabled']) {
            return $this->create_test_coupon($coupon_data);
        }

        $api_url = rtrim($settings['remote_url'], '/') . '/wp-json/wc/v3/coupons';
        
        $response = wp_remote_post($api_url, [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($settings['consumer_key'] . ':' . $settings['consumer_secret']),
                'Content-Type' => 'application/json',
            ],
            'body' => wp_json_encode($coupon_data),
            'timeout' => 30,
            'sslverify' => $this->should_verify_ssl($settings['remote_url']),
        ]);

        if (is_wp_error($response)) {
            $this->logger->log_error('Coupon creation failed', [
                'error' => $response->get_error_message(),
                'coupon_code' => $coupon_data['code'] ?? 'unknown'
            ]);
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 201) {
            $result = json_decode(wp_remote_retrieve_body($response), true);
            
            $this->logger->log_info('Coupon created successfully', [
                'coupon_code' => $result['code'],
                'remote_id' => $result['id']
            ]);
            
            return $result;
        }

        $this->logger->log_error('Coupon creation failed', [
            'status_code' => $status_code,
            'coupon_code' => $coupon_data['code'] ?? 'unknown'
        ]);
        
        return false;
    }

    /**
     * Create test coupon for development
     */
    private function create_test_coupon($coupon_data)
    {
        $this->logger->log_info('Test coupon created (API disabled)', [
            'coupon_code' => $coupon_data['code'] ?? 'unknown'
        ]);

        return [
            'id' => rand(1000, 9999),
            'code' => $coupon_data['code'],
            'amount' => $coupon_data['amount'],
            'test_mode' => true
        ];
    }

    /**
     * Determine SSL verification based on URL
     */
    private function should_verify_ssl($url)
    {
        // Disable SSL verification for localhost/development
        $dev_patterns = ['localhost', '127.0.0.1', '.local', '.test', '.dev'];
        
        foreach ($dev_patterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check if API is ready
     */
    public function is_api_ready()
    {
        $settings = $this->integration->get_unified_settings();
        
        return $settings['api_enabled'] && 
               !empty($settings['remote_url']) && 
               !empty($settings['consumer_key']) && 
               !empty($settings['consumer_secret']) &&
               $settings['connection_status'] === 'connected';
    }
}
