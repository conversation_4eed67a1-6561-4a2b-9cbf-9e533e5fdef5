<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Email Notifications
 * 
 * Handles email notifications for gift card processing events
 * 
 * @since 1.0.0
 */
class BSA_Gift_Card_Email_Notifications
{
    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * Email settings
     */
    protected $email_settings;

    /**
     * Constructor
     */
    private function __construct()
    {
        $this->refresh_email_settings();
        add_action('init', [$this, 'refresh_email_settings']);
    }

    /**
     * Get singleton instance
     */
    public static function get_instance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Refresh email settings
     */
    public function refresh_email_settings()
    {
        $this->email_settings = get_option('bsa_gift_card_email_settings', [
            'enable_notifications' => 'yes',
            'notification_email' => get_option('admin_email'),
            'enable_success_notifications' => 'yes',
            'enable_failure_notifications' => 'yes',
            'include_customer_details' => 'yes',
            'success_subject' => 'BSA Gift Card Successfully Created - Order #{order_id}',
            'failure_subject' => 'BSA Gift Card Creation Failed - Order #{order_id}'
        ]);
    }

    /**
     * Send gift card success notification
     * 
     * @param array $gift_card_data Gift card data
     * @param array $coupon_data Coupon creation data
     */
    public function send_success_notification($gift_card_data, $coupon_data)
    {
        if (
            !$this->should_send_notifications() ||
            $this->email_settings['enable_success_notifications'] !== 'yes'
        ) {
            return;
        }

        $order = wc_get_order($gift_card_data['order_id']);
        if (!$order) {
            return;
        }

        $email_data = array_merge($gift_card_data, $coupon_data, [
            'order' => $order,
            'notification_type' => 'success'
        ]);

        $subject = $this->parse_email_placeholders(
            $this->email_settings['success_subject'],
            $email_data
        );

        $message = $this->build_success_email_content($email_data);
        $headers = $this->get_email_headers();

        $this->send_notification_email($subject, $message, $headers);
    }

    /**
     * Send gift card failure notification
     * 
     * @param array $gift_card_data Gift card data
     * @param string $error_message Error message
     * @param array $error_details Additional error details
     */
    public function send_failure_notification($gift_card_data, $error_message, $error_details = [])
    {
        if (
            !$this->should_send_notifications() ||
            $this->email_settings['enable_failure_notifications'] !== 'yes'
        ) {
            return;
        }

        $order = wc_get_order($gift_card_data['order_id']);
        if (!$order) {
            return;
        }

        $email_data = array_merge($gift_card_data, [
            'order' => $order,
            'error_message' => $error_message,
            'error_details' => $error_details,
            'notification_type' => 'failure'
        ]);

        $subject = $this->parse_email_placeholders(
            $this->email_settings['failure_subject'],
            $email_data
        );

        $message = $this->build_failure_email_content($email_data);
        $headers = $this->get_email_headers();

        $this->send_notification_email($subject, $message, $headers);
    }

    /**
     * Send summary notification - UPDATED TO HANDLE BOTH REAL AND TEST DATA
     * 
     * @param int|string $order_id Order ID or test order ID
     * @param int $success_count Number of successful creations
     * @param int $failure_count Number of failed creations
     * @param array $details Detailed results
     * @param bool $is_test Whether this is a test email
     */
    public function send_summary_notification($order_id, $success_count, $failure_count, $details = [], $is_test = false)
    {
        if (!$this->should_send_notifications()) {
            return false;
        }

        // Handle test emails differently
        if ($is_test) {
            return $this->send_test_email_with_fake_data($order_id, $success_count, $failure_count, $details);
        }

        // Original logic for real orders
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }

        $email_data = [
            'order' => $order,
            'order_id' => $order_id,
            'success_count' => $success_count,
            'failure_count' => $failure_count,
            'total_count' => $success_count + $failure_count,
            'details' => $details,
            'notification_type' => 'summary',
            'is_test' => false
        ];

        $subject_template = $failure_count > 0 ?
            'BSA Gift Cards Processing Complete - Order #{order_id} ({success_count} Success, {failure_count} Failed)' :
            'BSA Gift Cards Successfully Created - Order #{order_id} ({success_count} Created)';

        $subject = $this->parse_email_placeholders($subject_template, $email_data);
        $message = $this->build_summary_email_content($email_data);
        $headers = $this->get_email_headers();

        return $this->send_notification_email($subject, $message, $headers);
    }

    /**
     * Send test email using fake data but same template - ADD THIS NEW METHOD
     */
    private function send_test_email_with_fake_data($fake_order_id, $success_count, $failure_count, $details)
    {
        // Create fake order-like data structure
        $fake_order_data = [
            'id' => $fake_order_id,
            'order_number' => str_replace('TEST-', '', $fake_order_id),
            'billing_first_name' => 'Max',
            'billing_last_name' => 'Mustermann',
            'billing_email' => '<EMAIL>',
            'date_created' => current_time('mysql'),
            'edit_order_url' => '#', // No real link for test
            'is_test' => true
        ];

        $email_data = [
            'order' => (object) $fake_order_data, // Convert to object to mimic WC_Order
            'order_id' => $fake_order_id,
            'success_count' => $success_count,
            'failure_count' => $failure_count,
            'total_count' => $success_count + $failure_count,
            'details' => $details,
            'notification_type' => 'summary',
            'is_test' => true,
            'test_timestamp' => current_time('d.m.Y H:i:s')
        ];

        $subject = 'BSA Gift Card - Test Email - ' . current_time('d.m.Y H:i:s');
        $message = $this->build_summary_email_content($email_data); // Same method!
        $headers = $this->get_email_headers();

        return $this->send_notification_email($subject, $message, $headers);
    }


    /**
     * Check if notifications should be sent
     */
    private function should_send_notifications()
    {
        return $this->email_settings['enable_notifications'] === 'yes' &&
            !empty($this->email_settings['notification_email']) &&
            is_email($this->email_settings['notification_email']);
    }

    /**
     * Build success email content
     */
    private function build_success_email_content($data)
    {
        $order = $data['order'];

        ob_start();
?>
        <!DOCTYPE html>
        <html>

        <head>
            <meta charset="UTF-8">
            <title>BSA Gift Card Created Successfully</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                }

                .header {
                    background: #0073aa;
                    color: white;
                    padding: 20px;
                    text-align: center;
                }

                .content {
                    padding: 20px;
                }

                .success {
                    background: #d1eddd;
                    border-left: 4px solid #46b450;
                    padding: 12px;
                    margin: 16px 0;
                }

                .info-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }

                .info-table th,
                .info-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }

                .info-table th {
                    background-color: #f5f5f5;
                }

                .footer {
                    background: #f8f9fa;
                    padding: 15px;
                    font-size: 12px;
                    color: #666;
                }
            </style>
        </head>

        <body>
            <div class="header">
                <h1>✅ BSA Gift Card Successfully Created</h1>
            </div>

            <div class="content">
                <div class="success">
                    Eine BSA-Geschenkkarte wurde erfolgreich erstellt.
                </div>

                <h3>Gift Card Details</h3>
                <table class="info-table">
                    <tr>
                        <th>Coupon Code</th>
                        <td><strong><?php echo esc_html($data['coupon_code'] ?? 'N/A'); ?></strong></td>
                    </tr>
                    <tr>
                        <th>Betrag</th>
                        <td><?php echo wc_price($data['amount'] ?? 0); ?></td>
                    </tr>
                    <tr>
                        <th>Remote Coupon ID</th>
                        <td><?php echo esc_html($data['remote_id'] ?? 'N/A'); ?></td>
                    </tr>
                    <tr>
                        <th>Ablaufdatum</th>
                        <td><?php echo esc_html($data['expiry_date'] ?? 'N/A'); ?></td>
                    </tr>
                    <tr>
                        <th>Von</th>
                        <td><?php echo esc_html($data['from_name'] ?? 'N/A'); ?></td>
                    </tr>
                    <tr>
                        <th>Zu</th>
                        <td>
                            <?php
                            if ($data['format'] === 'digital') {
                                echo esc_html($data['recipient_email'] ?? 'N/A') . ' (Digital)';
                            } else {
                                echo esc_html($data['recipient_name'] ?? 'N/A') . ' (Print)';
                            }
                            ?>
                        </td>
                    </tr>
                    <?php if (!empty($data['template'])): ?>
                        <tr>
                            <th>Template</th>
                            <td><?php echo esc_html($data['template']); ?></td>
                        </tr>
                    <?php endif; ?>
                </table>

                <?php if ($this->email_settings['include_customer_details'] === 'yes'): ?>
                    <h3>Order Information</h3>
                    <table class="info-table">
                        <tr>
                            <th>Order ID</th>
                            <td><a href="<?php echo esc_url($order->get_edit_order_url()); ?>">#<?php echo $order->get_order_number(); ?></a></td>
                        </tr>
                        <tr>
                            <th>Customer</th>
                            <td><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></td>
                        </tr>
                        <tr>
                            <th>Customer Email</th>
                            <td><?php echo esc_html($order->get_billing_email()); ?></td>
                        </tr>
                        <tr>
                            <th>Order Date</th>
                            <td><?php echo esc_html($order->get_date_created()->format('Y-m-d H:i:s')); ?></td>
                        </tr>
                    </table>
                <?php endif; ?>

                <?php if (!empty($data['message'])): ?>
                    <h3>Gift Card Message</h3>
                    <div style="background: #f9f9f9; padding: 15px; border-radius: 4px; font-style: italic;">
                        <?php echo esc_html($data['message']); ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="footer">
                <p>Diese Benachrichtigung wurde automatisch versendet von<?php echo esc_html(get_bloginfo('name')); ?>.</p>
                <p>Generated at: <?php echo current_time('Y-m-d H:i:s'); ?></p>
            </div>
        </body>

        </html>
    <?php
        return ob_get_clean();
    }

    /**
     * Build failure email content
     */
    private function build_failure_email_content($data)
    {
        $order = $data['order'];

        ob_start();
    ?>
        <!DOCTYPE html>
        <html>

        <head>
            <meta charset="UTF-8">
            <title>BSA-Geschenkkarte Erstellung fehlgeschlagen</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                }

                .header {
                    background: #d63638;
                    color: white;
                    padding: 20px;
                    text-align: center;
                }

                .content {
                    padding: 20px;
                }

                .error {
                    background: #fbeaea;
                    border-left: 4px solid #d63638;
                    padding: 12px;
                    margin: 16px 0;
                }

                .info-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }

                .info-table th,
                .info-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }

                .info-table th {
                    background-color: #f5f5f5;
                }

                .footer {
                    background: #f8f9fa;
                    padding: 15px;
                    font-size: 12px;
                    color: #666;
                }
            </style>
        </head>

        <body>
            <div class="header">
                <h1>❌ BSA-Geschenkkarte Erstellung fehlgeschlagen</h1>
            </div>

            <div class="content">
                <div class="error">
                    BSA-Geschenkkarte Erstellung fehlgeschlagen..
                </div>

                <h3>Error Details</h3>
                <table class="info-table">
                    <tr>
                        <th>Error Message</th>
                        <td><?php echo esc_html($data['error_message']); ?></td>
                    </tr>
                    <?php if (!empty($data['error_details']['response_code'])): ?>
                        <tr>
                            <th>Response Code</th>
                            <td><?php echo esc_html($data['error_details']['response_code']); ?></td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <th>Attempted Amount</th>
                        <td><?php echo wc_price($data['price'] ?? 0); ?></td>
                    </tr>
                </table>

                <?php if ($this->email_settings['include_customer_details'] === 'yes'): ?>
                    <h3>Order Information</h3>
                    <table class="info-table">
                        <tr>
                            <th>Order ID</th>
                            <td><a href="<?php echo esc_url($order->get_edit_order_url()); ?>">#<?php echo $order->get_order_number(); ?></a></td>
                        </tr>
                        <tr>
                            <th>Customer</th>
                            <td><?php echo esc_html($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()); ?></td>
                        </tr>
                        <tr>
                            <th>Customer Email</th>
                            <td><?php echo esc_html($order->get_billing_email()); ?></td>
                        </tr>
                    </table>
                <?php endif; ?>

                <h3>Empfohlene Maßnahmen</h3>
                <ul>
                    <li>Überprüfe die Verbindung zur externen API und die Zugangsdaten</li>
                    <li>Stelle sicher, dass die Ziel-Website erreichbar ist</li>
                    <li>Prüfe die Bestellung auf ungewöhnliche Daten</li>
                    <li>Erstelle den Gutschein bei Bedarf manuell</li>
                </ul>
            </div>

            <div class="footer">
                <p>This notification was sent automatically by <?php echo esc_html(get_bloginfo('name')); ?>.</p>
                <p>Generated at: <?php echo current_time('Y-m-d H:i:s'); ?></p>
            </div>
        </body>

        </html>
    <?php
        return ob_get_clean();
    }


    /**
     * Build summary email content - UPDATED TO HANDLE BOTH REAL AND TEST DATA
     */
    private function build_summary_email_content($data)
    {
        $order = $data['order'];
        $is_test = isset($data['is_test']) && $data['is_test'];

        ob_start();
    ?>
        <!DOCTYPE html>
        <html>

        <head>
            <meta charset="UTF-8">
            <title><?php echo $is_test ? 'BSA Gift Card - Test Email' : 'BSA-Gutschein Verarbeitung'; ?></title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                }

                .header {
                    background: #C4281F;
                    color: white;
                    padding: 20px;
                    text-align: center;
                }

                .content {
                    padding: 20px;
                }

                .info-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }

                .info-table th,
                .info-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }

                .info-table th {
                    background-color: #f5f5f5;
                }

                .footer {
                    background: #f8f9fa;
                    padding: 15px;
                    font-size: 12px;
                    color: #666;
                }

                .success {
                    color: #46b450;
                    font-weight: bold;
                }

                .failed {
                    color: #d63638;
                    font-weight: bold;
                }

                .test-notice {
                    background: #e7f3ff;
                    border: 1px solid #b3d9ff;
                    padding: 15px;
                    border-radius: 4px;
                    margin: 20px 0;
                }
            </style>
        </head>

        <body>
            <div class="header">
                <h1><?php echo $is_test ? 'BSA-Gutschein Test Email' : 'BSA-Gutschein Verarbeitung'; ?></h1>
                <?php if ($is_test): ?>
                    <p>Dies ist eine Test-E-Mail mit Beispieldaten</p>
                <?php endif; ?>
            </div>

            <div class="content">
                <?php if ($is_test): ?>
                    <div class="test-notice">
                        <h4 style="margin-top: 0;">ℹ️ Test-Information</h4>
                        <p>Dies ist eine Test-E-Mail mit Beispieldaten. Keine echten Gutscheine wurden erstellt.</p>
                    </div>
                <?php endif; ?>

                <h3>Bestellinformation</h3>
                <table class="info-table">
                    <tr>
                        <th>Bestellung-ID</th>
                        <td>
                            <?php if ($is_test): ?>
                                #<?php echo esc_html($data['order_id']); ?>
                            <?php else: ?>
                                <a href="<?php echo esc_url($order->get_edit_order_url()); ?>">#<?php echo $order->get_order_number(); ?></a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Erstellungsdatum</th>
                        <td>
                            <?php if ($is_test): ?>
                                <?php echo esc_html($data['test_timestamp'] ?? current_time('d.m.Y H:i:s')); ?>
                            <?php else: ?>
                                <?php echo current_time('d.m.Y H:i:s'); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>

                <?php if (!empty($data['details'])): ?>
                    <table class="info-table">
                        <tr>
                            <th>Produkt</th>
                            <th>Betrag</th>
                            <th>Format</th>
                            <th>Status</th>
                            <th>Gutscheincode</th>
                        </tr>
                        <?php foreach ($data['details'] as $detail): ?>
                            <tr>
                                <td><?php echo esc_html($detail['item_name'] ?? 'BSA Gift Card'); ?></td>
                                <td><?php echo wc_price($detail['amount'] ?? 0); ?></td>
                                <td><?php echo esc_html(ucfirst($detail['format'] ?? '')); ?></td>
                                <td>
                                    <?php if ($detail['success']): ?>
                                        <span class="success">✅ Erfolgreich</span>
                                    <?php else: ?>
                                        <span class="failed">❌ Fehlgeschlagen</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($detail['success']): ?>
                                        <span style="font-family: monospace; background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">
                                            <?php echo esc_html($detail['coupon_code'] ?? 'N/A'); ?>
                                        </span>
                                    <?php else: ?>
                                        <span style="color: #d63638;"><?php echo esc_html($detail['error'] ?? 'Unbekannter Fehler'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>

                <?php if (!$is_test && $data['failure_count'] > 0): ?>
                    <h3>⚠️ Erforderliche Maßnahmen</h3>
                    <div style="background: #fbeaea; border-left: 4px solid #d63638; padding: 12px; margin: 16px 0;">
                        <p><strong>Es gab Fehler bei der Gutschein-Erstellung.</strong> Bitte überprüfen:</p>
                        <ul>
                            <li>Die Verbindung zur externen API</li>
                            <li>Die API-Zugangsdaten in den Einstellungen</li>
                            <li>Die Erreichbarkeit der Ziel-Website</li>
                            <li>Erstellen Sie fehlgeschlagene Gutscheine manuell falls nötig</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>

            <div class="footer">
                <?php if ($is_test): ?>
                    <p><strong>Test-E-Mail von:</strong> <?php echo esc_html(get_bloginfo('name')); ?></p>
                    <p><strong>Gesendet am:</strong> <?php echo esc_html($data['test_timestamp'] ?? current_time('d.m.Y H:i:s')); ?></p>
                    <p><em>Diese E-Mail wurde durch den "Test Email" Button ausgelöst.</em></p>
                <?php else: ?>
                    <p>Diese Benachrichtigung wurde automatisch von <?php echo esc_html(get_bloginfo('name')); ?> versendet.</p>
                    <p>Erstellt am: <?php echo current_time('d.m.Y H:i:s'); ?></p>
                <?php endif; ?>
            </div>
        </body>

        </html>
    <?php
        return ob_get_clean();
    }



    /**
     * Parse email placeholders
     */
    private function parse_email_placeholders($template, $data)
    {
        $placeholders = [
            '{order_id}' => $data['order_id'] ?? '',
            '{coupon_code}' => $data['coupon_code'] ?? '',
            '{amount}' => wc_price($data['amount'] ?? 0),
            '{success_count}' => $data['success_count'] ?? 0,
            '{failure_count}' => $data['failure_count'] ?? 0,
            '{total_count}' => $data['total_count'] ?? 0,
            '{site_name}' => get_bloginfo('name'),
        ];

        return str_replace(array_keys($placeholders), array_values($placeholders), $template);
    }

    /**
     * Get email headers
     */
    private function get_email_headers()
    {
        return [
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        ];
    }

    /**
     * Send notification email
     */
    private function send_notification_email($subject, $message, $headers)
    {
        $to = $this->email_settings['notification_email'];

        $result = wp_mail($to, $subject, $message, $headers);

        if (!$result) {
            error_log('BSA Gift Card: Failed to send notification email to ' . $to);
        }

        return $result;
    }

    /**
     * Send API connection failure notification
     * 
     * @param array $test_data API test failure data
     */
    public function send_api_failure_notification($test_data)
    {
        if (!$this->should_send_notifications()) {
            return;
        }

        $subject = sprintf(
            __('BSA Gift Card API Connection Failed - %s', 'bsa-gift-card'),
            $test_data['remote_url']
        );

        $message = $this->build_api_failure_email_content($test_data);
        $headers = $this->get_email_headers();

        return $this->send_notification_email($subject, $message, $headers);
    }

    /**
     * Build API failure email content
     */
    private function build_api_failure_email_content($data)
    {
        ob_start();
    ?>
        <!DOCTYPE html>
        <html>

        <head>
            <meta charset="UTF-8">
            <title>BSA Gift Card API Connection Failed</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                }

                .header {
                    background: #d63638;
                    color: white;
                    padding: 20px;
                    text-align: center;
                }

                .content {
                    padding: 20px;
                }

                .error {
                    background: #fbeaea;
                    border-left: 4px solid #d63638;
                    padding: 12px;
                    margin: 16px 0;
                }

                .info-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }

                .info-table th,
                .info-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }

                .info-table th {
                    background-color: #f5f5f5;
                }

                .footer {
                    background: #f8f9fa;
                    padding: 15px;
                    font-size: 12px;
                    color: #666;
                }

                .code-block {
                    background: #f8f9f9;
                    padding: 10px;
                    border-radius: 4px;
                    font-family: monospace;
                    font-size: 12px;
                }
            </style>
        </head>

        <body>
            <div class="header">
                <h1>🔴 BSA Gift Card API Connection Failed</h1>
            </div>

            <div class="content">
                <div class="error">
                    The API connection test to your BSA Gift Card remote shop has failed.
                </div>

                <h3>Connection Test Details</h3>
                <table class="info-table">
                    <tr>
                        <th>Remote URL</th>
                        <td><?php echo esc_html($data['remote_url']); ?></td>
                    </tr>
                    <tr>
                        <th>Test Timestamp</th>
                        <td><?php echo esc_html($data['test_timestamp']); ?></td>
                    </tr>
                    <tr>
                        <th>Tested By</th>
                        <td><?php echo esc_html($data['tested_by_user']); ?></td>
                    </tr>
                    <tr>
                        <th>Error Type</th>
                        <td><?php echo esc_html($data['error_type']); ?></td>
                    </tr>
                    <tr>
                        <th>Error Message</th>
                        <td><?php echo esc_html($data['error_message']); ?></td>
                    </tr>
                </table>

                <?php if (!empty($data['test_details'])): ?>
                    <h3>Technical Details</h3>
                    <table class="info-table">
                        <?php if (isset($data['test_details']['response_code'])): ?>
                            <tr>
                                <th>HTTP Response Code</th>
                                <td><?php echo esc_html($data['test_details']['response_code']); ?></td>
                            </tr>
                        <?php endif; ?>

                        <?php if (isset($data['test_details']['response_time_ms'])): ?>
                            <tr>
                                <th>Response Time</th>
                                <td><?php echo esc_html($data['test_details']['response_time_ms']); ?> ms</td>
                            </tr>
                        <?php endif; ?>

                        <?php if (isset($data['test_details']['error_code'])): ?>
                            <tr>
                                <th>WordPress Error Code</th>
                                <td><?php echo esc_html($data['test_details']['error_code']); ?></td>
                            </tr>
                        <?php endif; ?>

                        <?php if (isset($data['test_details']['test_endpoint'])): ?>
                            <tr>
                                <th>Test Endpoint</th>
                                <td><?php echo esc_html($data['test_details']['test_endpoint']); ?></td>
                            </tr>
                        <?php endif; ?>
                    </table>
                <?php endif; ?>

                <?php if (!empty($data['test_details']['response_preview'])): ?>
                    <h3>Response Preview</h3>
                    <div class="code-block">
                        <?php echo esc_html($data['test_details']['response_preview']); ?>
                        <?php if (strlen($data['test_details']['response_preview']) >= 200): ?>
                            <br><em>[Response truncated...]</em>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <h3>Recommended Actions</h3>
                <ul>
                    <li><strong>Check Remote URL:</strong> Verify the shop URL is correct and accessible</li>
                    <li><strong>Verify API Credentials:</strong> Ensure Consumer Key and Secret are valid</li>
                    <li><strong>Check Remote Shop:</strong> Confirm WooCommerce is active on the remote site</li>
                    <li><strong>Network Issues:</strong> Check for firewall or hosting restrictions</li>
                    <li><strong>SSL Certificate:</strong> Verify SSL certificate is valid on remote shop</li>
                    <li><strong>API Permissions:</strong> Ensure API credentials have proper permissions</li>
                </ul>

                <h3>Next Steps</h3>
                <p>
                    1. Review and correct the issues above<br>
                    2. Go to <strong>WooCommerce → Integrations → BSA Gift Card API</strong><br>
                    3. Click "Test Connection" again<br>
                    4. Gift card coupons cannot be created until API connection is successful
                </p>
            </div>

            <div class="footer">
                <p><strong>Site:</strong> <?php echo esc_html(get_bloginfo('name')); ?> (<?php echo esc_html(home_url()); ?>)</p>
                <p><strong>Generated:</strong> <?php echo current_time('d.m.Y H:i:s'); ?></p>
                <p><strong>Log Location:</strong> WooCommerce → Status → Logs → bsa-gift-card-api</p>
            </div>
        </body>

        </html>
    <?php
        return ob_get_clean();
    }

}
