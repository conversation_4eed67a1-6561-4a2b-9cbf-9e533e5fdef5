<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Settings Registration
 */
class BSA_Gift_Card_Settings
{
    public function __construct()
    {
        $this->init_hooks();
    }

    private function init_hooks()
    {
        add_filter('woocommerce_integrations', [$this, 'register_integration']);
    }

    public function register_integration($integrations)
    {
        // Register the main settings tab
        if (class_exists('BSA_Gift_Card_Integration')) {
            $integrations[] = 'BSA_Gift_Card_Integration';
        }

        // Register the test tools tab
        if (class_exists('BSA_Gift_Card_Test_Integration')) {
            $integrations[] = 'BSA_Gift_Card_Test_Integration';
        }

        return $integrations;
    }

    public static function get_integration()
    {
        return BSA_Gift_Card_Integration::get_instance();
    }
}
