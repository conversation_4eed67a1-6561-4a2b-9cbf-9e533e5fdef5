<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card PDF Generator - Format-Based Version
 * 
 * Generates files based on gift card format:
 * - Digital: PDF + TXT
 * - Print: TXT only
 * 
 * @since 1.0.0
 */
class BSA_Gift_Card_PDF_Generator
{
    /**
     * Template directory path
     */
    private $template_dir;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->template_dir = BSA_GIFT_CARD_PLUGIN_DIR . 'templates/';
    }

    /**
     * Get template file path based on selected design
     *
     * @param string $template_name Template name from form (geschenk, geburtstag, etc.)
     * @return string Template file path
     */
    private function get_template_path($template_name)
    {
        // Map template names to PDF file numbers - FIXED to match frontend values
        $template_map = [
            'Freude schenken' => '1',   // bsa_geschenkgutschein_digital_1.pdf
            'Geschenk' => '1',          // Backward compatibility
            'Geburtstag' => '2',        // bsa_geschenkgutschein_digital_2.pdf
            'Ostern' => '3',            // bsa_geschenkgutschein_digital_3.pdf
            'Weihnachten' => '4'        // bsa_geschenkgutschein_digital_4.pdf
        ];

        // Get the file number, default to 1 if template not found
        $file_number = isset($template_map[$template_name]) ? $template_map[$template_name] : '1';

        // Construct the full path
        $template_path = $this->template_dir . 'bsa_geschenkgutschein_digital_' . $file_number . '.pdf';

        // Log which template is being used
        error_log('BSA Gift Card PDF: Using template: ' . $template_name . ' -> File: ' . basename($template_path));

        // Fallback to template 1 if specific template doesn't exist
        if (!file_exists($template_path)) {
            error_log('BSA Gift Card PDF: Template not found: ' . $template_path . ', using fallback');
            $template_path = $this->template_dir . 'bsa_geschenkgutschein_digital_1.pdf';
        }

        return $template_path;
    }


    /**
     * Generate files for gift card based on format
     * 
     * @param array $gift_card_data Gift card data
     * @return string|false File path or false on failure
     */
    public function generate_gift_card_pdf($gift_card_data)
    {
        // Validate required data
        if (!$this->validate_gift_card_data($gift_card_data)) {
            error_log('BSA Gift Card PDF: Invalid gift card data provided');
            return false;
        }

        try {
            // Create uploads directory if it doesn't exist
            $upload_dir = wp_upload_dir();
            $gift_card_dir = $upload_dir['basedir'] . '/bsa-gift-cards/';

            if (!file_exists($gift_card_dir)) {
                $created = wp_mkdir_p($gift_card_dir);
                if (!$created) {
                    error_log('BSA Gift Card PDF: Failed to create directory: ' . $gift_card_dir);
                    return false;
                }
            }

            // Generate base filename
            $base_filename = 'gift-card-' . $gift_card_data['code'] . '-' . date('Y-m-d-H-i-s');

            // Get format (default to digital if not specified)
            $format = $gift_card_data['format'] ?? 'digital';

            error_log('BSA Gift Card PDF: Processing format: ' . $format);

            if ($format === 'digital') {
                // Digital format: Create PDF + TXT TODO: Send email with PDF
                return $this->create_digital_files($gift_card_data, $gift_card_dir, $base_filename);
            } else {
                // Print format: Create TXT only TODO: Workflow for next steps
                return $this->create_print_files($gift_card_data, $gift_card_dir, $base_filename);
            }
        } catch (Exception $e) {
            error_log('BSA Gift Card PDF Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create files for digital format (PDF + TXT)
     * 
     * @param array $gift_card_data Gift card data
     * @param string $gift_card_dir Directory path
     * @param string $base_filename Base filename
     * @return string|false PDF file path or false on failure
     */
    private function create_digital_files($gift_card_data, $gift_card_dir, $base_filename)
    {
        $pdf_file = $gift_card_dir . $base_filename . '.pdf';
        // $txt_file = $gift_card_dir . $base_filename . '_data.txt'; TODO: Remove, only for a print version

        // Try to use FPDI if available, otherwise copy template
        if ($this->has_fpdi_library()) {
            error_log('BSA Gift Card PDF: Using FPDI for digital format');
            $pdf_result = $this->create_pdf_with_fpdi($gift_card_data, $pdf_file);
        } else {
            error_log('BSA Gift Card PDF: Using template copy for digital format');
            $pdf_result = $this->copy_template_file($pdf_file, $gift_card_data['template'] ?? 'geschenk');


        }

        // Create TXT file TODO: Remove, only for a print version
        // $txt_content = $this->create_data_overlay($gift_card_data);
        // $txt_result = file_put_contents($txt_file, $txt_content);

        if ($pdf_result !== false) {
            error_log('BSA Gift Card PDF: Digital files created successfully');
            return $pdf_file;
        }

        error_log('BSA Gift Card PDF: Failed to create digital files');
        return false;
    }

    /**
     * Create files for print format (TXT only)
     * 
     * @param array $gift_card_data Gift card data
     * @param string $gift_card_dir Directory path
     * @param string $base_filename Base filename
     * @return string|false TXT file path or false on failure
     */
    private function create_print_files($gift_card_data, $gift_card_dir, $base_filename)
    {
        $txt_file = $gift_card_dir . $base_filename . '_print_data.txt';

        // Create enhanced TXT file for print format
        $txt_content = $this->create_print_data_overlay($gift_card_data);
        $txt_result = file_put_contents($txt_file, $txt_content);

        if ($txt_result !== false) {
            error_log('BSA Gift Card PDF: Print TXT file created successfully');
            return $txt_file;
        }

        error_log('BSA Gift Card PDF: Failed to create print TXT file');
        return false;
    }

    /**
     * Check if FPDI library is available
     * 
     * @return bool
     */
    private function has_fpdi_library()
    {
        $autoload_path = BSA_GIFT_CARD_PLUGIN_DIR . 'vendor/autoload.php';
        return file_exists($autoload_path);
    }

    /**
     * Create PDF using FPDI library
     * 
     * @param array $gift_card_data Gift card data
     * @param string $output_path Output file path
     * @return bool Success status
     */
    private function create_pdf_with_fpdi($gift_card_data, $output_path)
    {
        try {
            require_once BSA_GIFT_CARD_PLUGIN_DIR . 'vendor/autoload.php';

            // Get the correct template based on selected design
            $template_path = $this->get_template_path($gift_card_data['template'] ?? 'geschenk');

            // Check if template exists
            if (!file_exists($template_path)) {
                error_log('BSA Gift Card PDF: Template not found: ' . $template_path);
                return $this->copy_template_file($output_path, $gift_card_data['template'] ?? 'geschenk');
            }

            // Create new FPDI instance
            $pdf = new \setasign\Fpdi\Fpdi();

            // Set source file
            $pageCount = $pdf->setSourceFile($template_path);
            error_log('BSA Gift Card PDF: Template loaded with FPDI, pages: ' . $pageCount);

            // Import first page
            $templateId = $pdf->importPage(1);

            // Add a page
            $pdf->AddPage();

            // Use the imported page as background
            $pdf->useTemplate($templateId, 0, 0);

            // Add gift card data overlay
            $this->add_data_to_pdf($pdf, $gift_card_data);

            // Output PDF to file
            $pdf->Output($output_path, 'F');

            error_log('BSA Gift Card PDF: FPDI PDF created successfully');
            return true;
        } catch (Exception $e) {
            error_log('BSA Gift Card PDF FPDI Error: ' . $e->getMessage());
            // Fallback to template copy
            return $this->copy_template_file($output_path, $gift_card_data['template'] ?? 'Freude schenken');
        }
    }

    /**
     * Add gift card data to PDF using FPDI
     * 
     * @param object $pdf FPDI PDF object
     * @param array $gift_card_data Gift card data
     */
    private function add_data_to_pdf($pdf, $gift_card_data)
    {

        // amount
        $pdf->SetXY(60, 131);
        $pdf->SetFont('Courier', 'B', 20);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->Cell(0, 10, number_format($gift_card_data['price'], 2, ',', '.'), 0, 0, 'L');


        // sender (von:)
        $pdf->SetXY(20, 152);
        $pdf->SetFont('Courier', '', 12);
        $pdf->SetTextColor(0, 0, 0);
        $sender = $gift_card_data['from_name'] ?? '';
        if (!empty($sender)) {
            $pdf->Cell(0, 8, $sender, 0, 0, 'L');
        }

        // recipient (für:)
        $pdf->SetXY(20, 142);
        $pdf->SetFont('Courier', '', 12);
        $pdf->SetTextColor(0, 0, 0);
        $recipient = $gift_card_data['to_name'];
        if (!empty($recipient)) {
            $pdf->Cell(0, 8, $recipient, 0, 0, 'L');
        }

        // date
        $pdf->SetXY(35, 162);
        $pdf->SetFont('Courier', '', 12);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->Cell(0, 8, date('d.m.Y'), 0, 0, 'L');

        // coupon code
        $pdf->SetFont('Courier', 'B', 18);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetXY(125, 136);
        $pdf->Cell(110, 10, $gift_card_data['code'], 0, 0, 'C');

        // message if exists
        if (!empty($gift_card_data['message'])) {
            $this->add_message_to_pdf($pdf, $gift_card_data['message']);
        }
    }

    /**
     * Add message to PDF with proper width and text wrapping control
     * 
     * @param object $pdf FPDI PDF object
     * @param string $message Message text
     */
    private function add_message_to_pdf($pdf, $message)
    {
        // Message area settings
        $message_x = 5;        // X position (left margin)
        $message_y = 190;       // Y position (top of message area)
        $message_width = 100;   // Maximum width in points
        $line_height = 4;       // Height of each line
        $max_lines = 4;         // Maximum number of lines
        $max_chars_per_line = 50; // Approximate characters per line

        // Set font for message
        $pdf->SetFont('Courier', '', 12);
        $pdf->SetTextColor(0, 0, 0);

        // Position for message
        $pdf->SetXY($message_x, $message_y);

        // Truncate message if too long
        $max_total_chars = $max_chars_per_line * $max_lines;
        if (strlen($message) > $max_total_chars) {
            $message = substr($message, 0, $max_total_chars - 3) . '...';
        }

        // Use MultiCell with specific width and line height
        // Parameters: width, height, text, border, align, fill
        $pdf->MultiCell($message_width, $line_height, $message, 0, 'L', false);
    }

    /**
     * Copy template file (fallback method)
     * 
     * @param string $output_path Output file path
     * @return bool Success status
     */
    private function copy_template_file($output_path, $template_name = 'geschenk')
    {
        $template_path = $this->get_template_path($template_name);

        if (file_exists($template_path)) {
            if (copy($template_path, $output_path)) {
                error_log('BSA Gift Card PDF: Template copied successfully (fallback): ' . basename($template_path));
                return true;
            }
        }

        error_log('BSA Gift Card PDF: Failed to copy template: ' . $template_path);
        return false;
    }


    /**
     * Create data overlay content for digital format
     * 
     * @param array $gift_card_data Gift card data
     * @return string Data content
     */
    private function create_data_overlay($gift_card_data)
    {
        $content = "BSA GESCHENKGUTSCHEIN - DIGITAL\n";
        $content .= "================================\n\n";
        $content .= "Wert: EUR " . number_format($gift_card_data['price'], 2, ',', '.') . "\n";

        $content .= "Gutscheincode: " . $gift_card_data['code'] . "\n";
        $content .= "Format: Digital / " . $gift_card_data['template'].  "\n\n";

        $content .= "EMPFÄNGER:\n";
        if (!empty($gift_card_data['to_name'])) {
            $content .= "Name: " . $gift_card_data['to_name'] . "\n";
        }
        $content .= "E-Mail: " . ($gift_card_data['to_email'] ?? 'N/A') . "\n";

        $content .= "\nABSENDER:\n";
        $content .= "Von: " . ($gift_card_data['from_name'] ?? 'N/A') . "\n";

        $content .= "\nDATEN:\n";
        $content .= "Ausgestellt am: " . date('d.m.Y') . "\n";
        $content .= "Gültig bis: " . date('d.m.Y', strtotime($gift_card_data['expire_date'])) . "\n";

        if (!empty($gift_card_data['message'])) {
            $content .= "\nPERSÖNLICHE NACHRICHT:\n";
            $content .= $gift_card_data['message'] . "\n";
        }

        $content .= "\nBESTELLINFORMATIONEN:\n";
        $content .= "Bestellnummer: " . ($gift_card_data['order_id'] ?? 'N/A') . "\n";
        $content .= "Erstellt: " . date('d.m.Y H:i:s') . "\n";

        return $content;
    }

    /**
     * Create enhanced data overlay for print format
     * 
     * @param array $gift_card_data Gift card data
     * @return string Data content
     */
    private function create_print_data_overlay($gift_card_data)
    {
        $content = "BSA GESCHENKGUTSCHEIN - DRUCK\n";
        $content .= "==============================\n\n";
        $content .= "*** DRUCKAUFTRAG ***\n\n";

        $content .= "GUTSCHEIN-DETAILS:\n";
        $content .= "Wert: EUR " . number_format($gift_card_data['price'], 2, ',', '.') . "\n";

        $content .= "Gutscheincode: " . $gift_card_data['code'] . "\n";
        $content .= "Format: Druck/Versand\n\n";

        $content .= "EMPFÄNGER (VERSANDADRESSE):\n";
        $content .= "Name: " . ($gift_card_data['to_name'] ?? 'N/A') . "\n";
        // Note: Shipping address will be added from order data
        $content .= "Adresse: [Wird aus Bestelldaten übernommen]\n\n";

        $content .= "ABSENDER:\n";
        $content .= "Von: " . ($gift_card_data['from_name'] ?? 'N/A') . "\n\n";

        $content .= "DRUCK-INFORMATIONEN:\n";
        $content .= "Ausstellungsdatum: " . date('d.m.Y') . "\n";
        $content .= "Gültigkeitsdatum: " . date('d.m.Y', strtotime($gift_card_data['expire_date'])) . "\n";
        $content .= "Druckdatum: " . date('d.m.Y H:i:s') . "\n\n";

        if (!empty($gift_card_data['message'])) {
            $content .= "PERSÖNLICHE NACHRICHT:\n";
            $content .= "\"" . $gift_card_data['message'] . "\"\n\n";
        }

        $content .= "BESTELLINFORMATIONEN:\n";
        $content .= "Bestellnummer: " . ($gift_card_data['order_id'] ?? 'N/A') . "\n";
        $content .= "Bestelldatum: " . ($gift_card_data['order_date'] ?? date('d.m.Y')) . "\n\n";

        $content .= "VERARBEITUNGSHINWEISE:\n";
        $content .= "- Gutschein auf BSA-Briefpapier drucken\n";
        $content .= "- In Geschenkumschlag verpacken\n";
        $content .= "- An Empfängeradresse versenden\n";
        $content .= "- Tracking-Nummer in Bestellung hinterlegen\n\n";

        $content .= "BSA-Akademie\n";
        $content .= "Hermann-Neuberger-Straße 3\n";
        $content .= "66123 Saarbrücken\n";

        return $content;
    }

    /**
     * Validate gift card data
     * 
     * @param array $data Gift card data
     * @return bool
     */
    private function validate_gift_card_data($data)
    {
        $required_fields = ['code', 'price', 'expire_date'];

        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                error_log('BSA Gift Card PDF: Missing required field: ' . $field);
                return false;
            }
        }

        return true;
    }

    /**
     * Get gift card data from order item
     * 
     * @param int $order_id Order ID
     * @param int $item_id Item ID
     * @return array|false Gift card data or false
     */
    public function get_gift_card_data_from_order($order_id, $item_id)
    {
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }

        $item = $order->get_item($item_id);
        if (!$item || $item->get_product()->get_type() !== 'bsa-gift-card') {
            return false;
        }

        $coupon_code = wc_get_order_item_meta($item_id, '_gift_card_coupon_code', true);
        if (empty($coupon_code)) {
            return false;
        }

        return [
            'code' => $coupon_code,
            'price' => wc_get_order_item_meta($item_id, 'gift_card_price', true),
            'from_name' => wc_get_order_item_meta($item_id, 'gift_card_from_name', true),
            'to_name' => wc_get_order_item_meta($item_id, 'gift_card_to_name', true),
            'to_email' => wc_get_order_item_meta($item_id, 'gift_card_to_email', true),
            'message' => wc_get_order_item_meta($item_id, 'gift_card_message', true),
            'format' => wc_get_order_item_meta($item_id, 'gift_card_format', true),
            'template' => wc_get_order_item_meta($item_id, 'gift_card_template', true),
            'expire_date' => $this->calculate_expiry_date(),
            'order_id' => $order_id,
            'order_date' => $order->get_date_created()->format('d.m.Y')
        ];
    }

    /**
     * Calculate expiry date - 3 years from creation, ending on December 31st
     * 
     * @return string Expiry date in Y-m-d format
     */
    private function calculate_expiry_date()
    {
        $current_year = (int) date('Y');
        $expiry_year = $current_year + 3;
        return $expiry_year . '-12-31';
    }
}
