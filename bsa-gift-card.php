<?php
/*
Plugin Name: [DHFPG Shop] BSA Geschenkgutschein
Description: Adds BSA-Geschenkgutschein product to DHFPG WooCommerce Shop with remote coupon creation.
Version: 1.0.12
Author: Mariusz
Text Domain: bsa-gift-card
Domain Path: /languages
Requires PHP: 7.4
Requires at least: 5.0
Tested up to: 6.4
Requires Plugins: WooCommerce
Requires WooCommerce: 6.0
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access forbidden.');
}

// Define plugin constants
define('BSA_GIFT_CARD_VERSION', '1.0.12');
define('BSA_GIFT_CARD_PLUGIN_FILE', __FILE__);
define('BSA_GIFT_CARD_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BSA_GIFT_CARD_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BSA_GIFT_CARD_MIN_PHP', '7.4');
define('BSA_GIFT_CARD_MIN_WP', '5.0');
define('BSA_GIFT_CARD_MIN_WC', '6.0');

// Backwards compatibility
define('BSA_GIFT_PRODUCT_PATH', BSA_GIFT_CARD_PLUGIN_DIR);
define('BSA_GIFT_PRODUCT_URL', BSA_GIFT_CARD_PLUGIN_URL);

/**
 * Main plugin class
 */
final class BSA_Gift_Card_Plugin
{
    /**
     * Single instance
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks()
    {
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);

        add_action('plugins_loaded', [$this, 'init'], 10);
        add_action('init', [$this, 'load_textdomain']);
    }

    /**
     * Plugin activation
     */
    public function activate()
    {
        if (!$this->check_requirements()) {
            wp_die(__('BSA Gift Card: Plugin requirements not met.', 'bsa-gift-card'));
        }

        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate()
    {
        flush_rewrite_rules();
    }

    /**
     * Check plugin requirements
     */
    private function check_requirements()
    {
        return version_compare(PHP_VERSION, BSA_GIFT_CARD_MIN_PHP, '>=') &&
            version_compare(get_bloginfo('version'), BSA_GIFT_CARD_MIN_WP, '>=') &&
            class_exists('WooCommerce');
    }

    /**
     * Initialize plugin
     */
    public function init()
    {
        if (!$this->check_requirements()) {
            add_action('admin_notices', [$this, 'requirements_notice']);
            return;
        }

        $this->include_files();
        $this->init_classes();
    }

    /**
     * Show requirements notice
     */
    public function requirements_notice()
    {
?>
        <div class="error">
            <p>
                <?php
                printf(
                    __('BSA Gift Card requires PHP %s+, WordPress %s+, and WooCommerce %s+ to function properly.', 'bsa-gift-card'),
                    BSA_GIFT_CARD_MIN_PHP,
                    BSA_GIFT_CARD_MIN_WP,
                    BSA_GIFT_CARD_MIN_WC
                );
                ?>
            </p>
        </div>
<?php
    }

    /**
     * Include required files
     */
    private function include_files()
    {
        $includes = [
            // Core functionality
            'includes/class-bsa-gift-card-coupon-generator.php',
            'includes/class-bsa-gift-card-email-notifications.php',
            'includes/class-bsa-gift-card-logger.php',
            'includes/class-bsa-gift-card-pdf-generator.php',
            'includes/class-bsa-gift-card.php',
            'includes/class-wc-product-bsa-gift-card.php',

            // Classes for admin
            'includes/class-bsa-gift-card-api-handler.php',
            'includes/class-bsa-gift-card-integration.php',
            'includes/class-bsa-gift-card-test-integration.php',
            'includes/class-bsa-gift-card-api-settings.php',
        ];

        foreach ($includes as $file) {
            $filepath = BSA_GIFT_CARD_PLUGIN_DIR . $file;
            if (file_exists($filepath)) {
                require_once $filepath;
            }
        }
    }

    /**
     * Initialize classes
     */
    private function init_classes()
    {
        try {
            // Initialize logger first
            BSA_Gift_Card_Logger::get_instance();

            // Initialize email notifications
            if (class_exists('BSA_Gift_Card_Email_Notifications')) {
                BSA_Gift_Card_Email_Notifications::get_instance();
            }

            // Initialize main functionality
            if (class_exists('BSA_Gift_Card')) {
                $gift_card = new BSA_Gift_Card();
                $gift_card->init();
            }

            if (class_exists('BSA_Gift_Card_Coupon_Generator')) {
                new BSA_Gift_Card_Coupon_Generator();
            }

            // Initialize admin settings
            if (is_admin() && class_exists('BSA_Gift_Card_Settings')) {
                new BSA_Gift_Card_Settings();
            }

            // Initialize integration classes for WooCommerce settings
            // Note: Integration classes are initialized via the BSA_Gift_Card_Settings class
            // which hooks into 'woocommerce_integrations' filter
        } catch (Exception $e) {
            add_action('admin_notices', function () use ($e) {
                echo '<div class="error"><p>BSA Gift Card Error: ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }

    /**
     * Load text domain
     */
    public function load_textdomain()
    {
        load_plugin_textdomain(
            'bsa-gift-card',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages'
        );
    }
}

// Initialize the plugin
BSA_Gift_Card_Plugin::instance();
