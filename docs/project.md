# BSA Gift Card - WooCommerce Custom Product Type Extension

## Project Description

A WordPress plugin that extends WooCommerce functionality to provide a custom gift card product type with advanced features including PDF generation, coupon management, and external API integration.

## Core Functionality

- **Custom Product Type**: Adds a specialized gift card product type to WooCommerce
- **REST API Integration**: Provides endpoints for coupon creation and management on external WordPress e-commerce sites
- **PDF Generation**: Automatically generates PDF gift cards with customizable templates
- **Coupon Management**: Creates and manages WooCommerce coupons for gift card redemption
- **Multi-language Support**: Compatible with WPML for internationalization
- **Remote API Communication**: Handles communication with external WordPress installations

## Plugin File Structure

```
bsa-gift-card/                                    # Plugin directory
├── bsa-gift-card.php                              # Main plugin file - entry point
├── composer.json                                  # Composer dependency configuration
├── composer.lock                                  # Locked dependency versions
├── assets/                                        # Static assets directory
│   ├── css/                                       # Stylesheets
│   │   ├── bsa-gift-card-admin.css               # Admin interface styles
│   │   └── bsa-gift-card.css                     # Frontend styles
│   ├── images/                                    # Gift card template images
│   │   ├── bsa_geschenkgutschein_geburtstag.png  # Birthday template
│   │   ├── bsa_geschenkgutschein_ostern.png      # Easter template
│   │   ├── bsa_geschenkgutschein_weihnachten.png # Christmas template
│   │   └── bsa_geschenkgutschein.png             # Default template
│   └── js/                                        # JavaScript files
│       ├── bsa_gift-card.js                      # Frontend functionality
│       └── bsa_gift-card-admin-settings.js       # Admin settings interface
├── docs/                                          # Documentation
│   └── project.md                                # Project documentation (this file)
├── includes/                                      # Core PHP classes
│   ├── class-bsa-gift-card-api-integration.php   # External API integration handler
│   ├── class-bsa-gift-card-api-settings.php      # API configuration and settings
│   ├── class-bsa-gift-card-coupon-generation.php # WooCommerce coupon creation logic
│   ├── class-bsa-gift-card-logger.php            # Logging and debugging utilities
│   ├── class-bsa-gift-card-pdf-generation.php    # PDF gift card generation
│   ├── class-bsa-gift-card-remote-api.php        # Remote WordPress API communication
│   ├── class-bsa-gift-card.php                   # Main plugin class and orchestration
│   └── class-wc-product-bsa-gift-card.php        # Custom WooCommerce product type
├── templates/                                     # Template files
│   ├── single-product/                           # WooCommerce product templates
│   │   └── add-to-cart/
│   │       └── bsa-gift-card.php                 # Custom add-to-cart template
│   ├── bsa_geschenkgutschein_digital_1.pdf       # PDF template variant 1
│   ├── bsa_geschenkgutschein_digital_2.pdf       # PDF template variant 2
│   ├── bsa_geschenkgutschein_digital_3.pdf       # PDF template variant 3
│   ├── bsa_geschenkgutschein_digital_4.pdf       # PDF template variant 4
│   └── bsa_geschenkgutschein_digital.pdf         # Default PDF template
└── vendor/                                        # Composer dependencies (auto-generated)
    ├── composer/                                  # Composer autoloader files
    ├── setasign/                                  # FPDI/TCPDF library for PDF manipulation
    └── autoload.php                               # Main autoloader entry point
```


## Dependencies

### WordPress Requirements
- **WordPress Version**: 6.8+
- **PHP Version**: 8.3+
- **WooCommerce**: 8.0+ (required for custom product types)

### PHP Extensions Required
- `gd` or `imagick` (for image processing)
- `curl` (for external API communication)
- `json` (for data serialization)

### Composer Dependencies
- **setasign/fpdi**: PDF manipulation library for filling PDF templates with dynamic data
- **setasign/tcpdf**: PDF generation library (dependency of FPDI)

### Optional Dependencies
- **WPML**: For multi-language support
- **WooCommerce PDF Invoices & Packing Slips**: For enhanced PDF functionality

## Key Classes and Their Responsibilities

### Core Classes
- **`BSA_Gift_Card`**: Main plugin orchestrator, handles initialization and hooks
- **`WC_Product_BSA_Gift_Card`**: Custom WooCommerce product type implementation
- **`BSA_Gift_Card_Coupon_Generation`**: Manages WooCommerce coupon creation and validation
- **`BSA_Gift_Card_PDF_Generation`**: Handles PDF gift card creation with templates

### API Classes
- **`BSA_Gift_Card_API_Integration`**: Manages external WordPress site integration
- **`BSA_Gift_Card_Remote_API`**: Handles remote API calls and authentication
- **`BSA_Gift_Card_API_Settings`**: Configuration management for API endpoints

### Utility Classes
- **`BSA_Gift_Card_Logger`**: Centralized logging for debugging and monitoring

## Technical Notes for AI Agents

### Code Architecture
- Plugin follows WordPress coding standards and WooCommerce extension patterns
- Uses object-oriented PHP with proper namespacing
- Implements WordPress hooks and filters for extensibility
- REST API endpoints follow WordPress REST API conventions

### Database Schema
- Utilizes WooCommerce's existing product and order tables
- Custom meta fields store gift card specific data
- Coupon codes are managed through WooCommerce's coupon system

### Security Considerations
- All API endpoints require proper authentication
- Input validation and sanitization implemented throughout
- Nonce verification for admin actions
- Capability checks for user permissions

## Plugin Workflow

### Gift Card Purchase Flow
1. Customer selects gift card product and configures options (amount, recipient, message)
2. Product is added to cart and purchased through WooCommerce checkout
3. Upon order completion, plugin generates:
   - WooCommerce coupon code for redemption
   - PDF gift card using selected template
   - Email notification to recipient (if configured)

### PDF Generation Process
1. Plugin selects appropriate PDF template from `/templates/` directory
2. Uses FPDI library to load existing PDF template
3. Overlays dynamic data (amount, code, recipient name, message) onto template
4. Saves generated PDF and optionally emails to recipient

### External API Integration
1. Remote WordPress sites can request coupon creation via REST API
2. Plugin validates API credentials and permissions
3. Creates WooCommerce coupon with specified parameters
4. Returns coupon details to requesting site

## Important File Locations for AI Agents

### Configuration Files
- **Main Plugin**: `bsa-gift-card.php` - Plugin header and initialization
- **Composer Config**: `composer.json` - Dependency management

### Core Logic Files
- **Product Type**: `includes/class-wc-product-bsa-gift-card.php` - Custom product implementation
- **PDF Generation**: `includes/class-bsa-gift-card-pdf-generation.php` - PDF creation logic
- **API Endpoints**: `includes/class-bsa-gift-card-api-integration.php` - REST API handlers

### Template Files
- **Product Display**: `templates/single-product/add-to-cart/bsa-gift-card.php` - Frontend form
- **PDF Templates**: `templates/*.pdf` - Base PDF files for gift card designs

### Asset Files
- **Frontend Styles**: `assets/css/bsa-gift-card.css` - Customer-facing styles
- **Admin Styles**: `assets/css/bsa-gift-card-admin.css` - Admin interface styles
- **JavaScript**: `assets/js/` - Frontend and admin functionality
