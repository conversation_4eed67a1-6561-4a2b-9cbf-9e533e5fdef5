# BSA Gift Card - Two Integration Tabs Setup

## Overview

The BSA Gift Card plugin now provides two separate WooCommerce integration tabs for better organization:

1. **BSA Gift Card Settings** - Configuration and settings only
2. **BSA Gift Card TESTS** - Test tools and debugging functionality

## Tab Structure

### Tab 1: BSA Gift Card Settings
**Location:** WooCommerce → Settings → Integration → BSA Gift Card Settings

**Purpose:** Configure the core functionality of the gift card system

**Contains:**
- **API Configuration**
  - Enable/Disable API
  - Remote Shop URL
  - Consumer Key
  - Consumer Secret

- **Email Notifications**
  - Enable/Disable Email Notifications
  - Notification Email Address

- **Logging**
  - Enable/Disable Detailed Logging

### Tab 2: BSA Gift Card TESTS
**Location:** WooCommerce → Settings → Integration → BSA Gift Card TESTS

**Purpose:** Test and debug the gift card system functionality

**Contains:**
- **Test Configuration**
  - Enable Test Mode
  - Auto-delete Test Orders

- **Test Tools**
  - API Connection Test
  - Create Test Gift Card Order
  - Individual Component Tests (Email, PDF)
  - Logs & Debug Access

## Implementation Details

### Files Created/Modified

1. **New File:** `includes/class-bsa-gift-card-test-integration.php`
   - Handles all test functionality
   - Extends WC_Integration
   - Delegates test operations to main integration class

2. **Modified:** `includes/class-bsa-gift-card-integration.php`
   - Renamed to "BSA Gift Card Settings"
   - Removed test tools from admin interface
   - Focused on configuration only

3. **Modified:** `includes/class-bsa-gift-card-api-settings.php`
   - Updated to register both integration tabs

4. **Modified:** `bsa-gift-card.php`
   - Added new test integration class to includes

### How It Works

1. **Registration:** Both integration classes are registered in the `BSA_Gift_Card_Settings::register_integration()` method

2. **Settings Tab:** Contains only configuration fields and settings

3. **Test Tab:** Contains test tools and delegates actual test execution to the main integration class methods

4. **Shared Functionality:** The test integration accesses the main integration instance to use existing test methods

## Usage Instructions

### For Administrators

1. **Initial Setup:**
   - Go to WooCommerce → Settings → Integration
   - Click on "BSA Gift Card Settings"
   - Configure your API settings and email preferences
   - Save settings

2. **Testing Configuration:**
   - Go to WooCommerce → Settings → Integration
   - Click on "BSA Gift Card TESTS"
   - Use the various test tools to verify your configuration
   - Check test results and debug any issues

### For Developers

1. **Adding New Settings:**
   - Add new form fields to `BSA_Gift_Card_Integration::init_form_fields()`
   - Settings will appear in the "BSA Gift Card Settings" tab

2. **Adding New Tests:**
   - Add new test tools to `BSA_Gift_Card_Test_Integration::admin_options()`
   - Add corresponding AJAX handlers to the test integration class
   - Delegate actual test logic to the main integration class

## Benefits

1. **Better Organization:** Clear separation between configuration and testing
2. **Improved UX:** Users can focus on settings without being overwhelmed by test tools
3. **Easier Maintenance:** Test functionality is isolated and easier to modify
4. **Cleaner Interface:** Each tab has a specific purpose and cleaner layout

## Troubleshooting

### If tabs don't appear:
1. Check that both integration classes are properly included in `bsa-gift-card.php`
2. Verify that WooCommerce is active and up to date
3. Check for PHP errors in the WordPress debug log

### If tests don't work:
1. Ensure settings are properly configured in the "Settings" tab first
2. Check that the main integration class methods are accessible
3. Verify AJAX handlers are properly registered

### If settings don't save:
1. Check WordPress user permissions
2. Verify nonce validation is working
3. Check for conflicts with other plugins
